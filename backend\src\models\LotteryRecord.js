const { DataTypes } = require('sequelize')
const { sequelize } = require('../config/database')

// 抽奖记录模型
const LotteryRecord = sequelize.define('LotteryRecord', {
  id: {
    type: DataTypes.BIGINT,
    primaryKey: true,
    autoIncrement: true
  },
  periodId: {
    type: DataTypes.BIGINT,
    allowNull: false,
    field: 'period_id'
  },
  userId: {
    type: DataTypes.BIGINT,
    allowNull: false,
    field: 'user_id'
  },
  platformId: {
    type: DataTypes.STRING(50),
    allowNull: false,
    field: 'platform_id'
  },
  prizeLevel: {
    type: DataTypes.INTEGER,
    field: 'prize_level'
  },
  prizeName: {
    type: DataTypes.STRING(100),
    field: 'prize_name'
  },
  prizeAmount: {
    type: DataTypes.DECIMAL(10, 2),
    defaultValue: 0,
    field: 'prize_amount'
  },
  isWinner: {
    type: DataTypes.BOOLEAN,
    defaultValue: false,
    field: 'is_winner'
  },
  drawResult: {
    type: DataTypes.JSON,
    field: 'draw_result'
  },
  status: {
    type: DataTypes.ENUM('待发放', '已发放', '已取消'),
    defaultValue: '待发放'
  },
  distributedAt: {
    type: DataTypes.DATE,
    allowNull: true,
    field: 'distributed_at'
  },
  distributedBy: {
    type: DataTypes.BIGINT,
    allowNull: true,
    field: 'distributed_by'
  },
  distributionRemark: {
    type: DataTypes.TEXT,
    allowNull: true,
    field: 'distribution_remark'
  },
  drawnAt: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW,
    field: 'drawn_at'
  }
}, {
  tableName: 'lottery_records',
  underscored: true,
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at'
})

module.exports = LotteryRecord
