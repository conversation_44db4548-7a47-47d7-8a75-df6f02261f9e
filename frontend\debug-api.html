<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API调试页面</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .result { margin: 10px 0; padding: 10px; border: 1px solid #ccc; background: #f9f9f9; }
        .error { background: #ffebee; border-color: #f44336; }
        .success { background: #e8f5e9; border-color: #4caf50; }
        button { margin: 5px; padding: 10px 20px; }
        pre { white-space: pre-wrap; word-wrap: break-word; }
    </style>
</head>
<body>
    <h1>API调试页面</h1>
    
    <div>
        <h3>步骤1：清理并检查localStorage</h3>
        <button onclick="clearAndCheck()">清理并检查localStorage</button>
    </div>
    
    <div>
        <h3>步骤2：测试基础API</h3>
        <button onclick="testHealth()">测试健康检查</button>
        <button onclick="testCurrentPeriod()">测试当前期次</button>
    </div>
    
    <div>
        <h3>步骤3：用户认证测试</h3>
        <button onclick="testUserLogin()">用户登录</button>
        <button onclick="testUserStatus()">测试用户状态</button>
    </div>
    
    <div>
        <h3>步骤4：模拟前端调用</h3>
        <button onclick="simulateFrontendCall()">模拟前端API调用</button>
    </div>
    
    <div id="results"></div>

    <script>
        const API_BASE = 'http://localhost:3000';
        
        function addResult(title, data, isError = false) {
            const results = document.getElementById('results');
            const div = document.createElement('div');
            div.className = `result ${isError ? 'error' : 'success'}`;
            div.innerHTML = `
                <h4>${title}</h4>
                <pre>${typeof data === 'string' ? data : JSON.stringify(data, null, 2)}</pre>
            `;
            results.appendChild(div);
        }
        
        function clearAndCheck() {
            localStorage.clear();
            const data = {
                cleared: true,
                remainingKeys: Object.keys(localStorage),
                timestamp: new Date().toISOString()
            };
            addResult('localStorage清理完成', data);
        }
        
        async function testHealth() {
            try {
                const response = await fetch(`${API_BASE}/health`);
                const data = await response.json();
                addResult('健康检查', { status: response.status, data });
            } catch (error) {
                addResult('健康检查失败', error.message, true);
            }
        }
        
        async function testCurrentPeriod() {
            try {
                const response = await fetch(`${API_BASE}/api/lottery/current-period`);
                const data = await response.json();
                addResult('当前期次API', { 
                    status: response.status, 
                    url: response.url,
                    data 
                });
            } catch (error) {
                addResult('当前期次API失败', error.message, true);
            }
        }
        
        async function testUserLogin() {
            try {
                const response = await fetch(`${API_BASE}/api/auth/login`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        username: 'testuser',
                        password: '123456'
                    })
                });
                const data = await response.json();
                
                if (data.success && data.data.token) {
                    localStorage.setItem('token', data.data.token);
                    addResult('用户登录成功', { 
                        status: response.status,
                        tokenSaved: true,
                        tokenPreview: data.data.token.substring(0, 20) + '...'
                    });
                } else {
                    addResult('用户登录失败', { status: response.status, data }, true);
                }
            } catch (error) {
                addResult('用户登录异常', error.message, true);
            }
        }
        
        async function testUserStatus() {
            try {
                const token = localStorage.getItem('token');
                if (!token) {
                    addResult('用户状态测试', '请先登录获取token', true);
                    return;
                }
                
                const response = await fetch(`${API_BASE}/api/lottery/user-status`, {
                    headers: {
                        'Authorization': `Bearer ${token}`
                    }
                });
                
                const data = await response.json();
                addResult('用户状态API', { 
                    status: response.status,
                    url: response.url,
                    headers: {
                        authorization: response.headers.get('authorization') ? '已发送' : '未发送'
                    },
                    data 
                });
            } catch (error) {
                addResult('用户状态API失败', {
                    message: error.message,
                    stack: error.stack
                }, true);
            }
        }
        
        async function simulateFrontendCall() {
            try {
                // 模拟前端的axios调用
                const token = localStorage.getItem('token');
                
                // 创建类似前端的请求配置
                const config = {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                };
                
                if (token) {
                    config.headers['Authorization'] = `Bearer ${token}`;
                }
                
                const response = await fetch(`${API_BASE}/api/lottery/user-status`, config);
                const data = await response.json();
                
                addResult('模拟前端调用', {
                    status: response.status,
                    statusText: response.statusText,
                    url: response.url,
                    requestHeaders: config.headers,
                    responseData: data
                });
                
            } catch (error) {
                addResult('模拟前端调用失败', {
                    name: error.name,
                    message: error.message,
                    stack: error.stack
                }, true);
            }
        }
        
        // 页面加载时自动执行基础检查
        window.onload = function() {
            clearAndCheck();
            testHealth();
        };
    </script>
</body>
</html>
