<template>
  <div class="min-h-screen bg-gradient-to-br from-gray-900 via-black to-gray-800 flex items-center justify-center p-4">
    <!-- 登录卡片 -->
    <div class="w-full max-w-md">
      <!-- Logo和标题 -->
      <div class="text-center mb-8">
        <div class="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-br from-gold-400 to-gold-600 rounded-full mb-4">
          <span class="text-2xl font-bold text-black">🎰</span>
        </div>
        <h1 class="text-3xl font-bold text-white mb-2">管理后台</h1>
        <p class="text-gray-400">澳门金沙抽奖平台</p>
      </div>

      <!-- 登录表单 -->
      <div class="bg-gradient-to-br from-gray-800 to-gray-900 rounded-2xl border border-gold-500/30 p-8 shadow-2xl">
        <form @submit.prevent="handleLogin" class="space-y-6">
          <!-- 用户名输入 -->
          <div>
            <label for="username" class="block text-sm font-medium text-gray-300 mb-2">
              管理员账号
            </label>
            <div class="relative">
              <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <span class="text-gray-400">👤</span>
              </div>
              <input
                id="username"
                v-model="loginForm.username"
                type="text"
                required
                class="w-full pl-10 pr-4 py-3 bg-gray-700/50 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-gold-500 focus:border-transparent transition-all"
                placeholder="请输入管理员账号"
                :disabled="loading"
              />
            </div>
          </div>

          <!-- 密码输入 -->
          <div>
            <label for="password" class="block text-sm font-medium text-gray-300 mb-2">
              密码
            </label>
            <div class="relative">
              <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <span class="text-gray-400">🔒</span>
              </div>
              <input
                id="password"
                v-model="loginForm.password"
                :type="showPassword ? 'text' : 'password'"
                required
                class="w-full pl-10 pr-12 py-3 bg-gray-700/50 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-gold-500 focus:border-transparent transition-all"
                placeholder="请输入密码"
                :disabled="loading"
              />
              <button
                type="button"
                @click="showPassword = !showPassword"
                class="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-300 transition-colors"
                :disabled="loading"
              >
                <span v-if="showPassword">👁️</span>
                <span v-else>🙈</span>
              </button>
            </div>
          </div>

          <!-- 记住登录 -->
          <div class="flex items-center justify-between">
            <label class="flex items-center">
              <input
                v-model="loginForm.remember"
                type="checkbox"
                class="w-4 h-4 text-gold-600 bg-gray-700 border-gray-600 rounded focus:ring-gold-500 focus:ring-2"
                :disabled="loading"
              />
              <span class="ml-2 text-sm text-gray-300">记住登录状态</span>
            </label>
            <button
              type="button"
              class="text-sm text-gold-400 hover:text-gold-300 transition-colors"
              @click="showForgotPassword = true"
              :disabled="loading"
            >
              忘记密码？
            </button>
          </div>

          <!-- 登录按钮 -->
          <button
            type="submit"
            :disabled="loading || !isFormValid"
            class="w-full py-3 px-4 bg-gradient-to-r from-gold-500 to-gold-600 hover:from-gold-600 hover:to-gold-700 disabled:from-gray-600 disabled:to-gray-700 disabled:cursor-not-allowed text-black font-bold rounded-lg transition-all duration-200 transform hover:scale-[1.02] disabled:scale-100"
          >
            <span v-if="loading" class="flex items-center justify-center">
              <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-black" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              登录中...
            </span>
            <span v-else>登录管理后台</span>
          </button>
        </form>

        <!-- 安全提示 -->
        <div class="mt-6 p-4 bg-blue-600/20 border border-blue-500/30 rounded-lg">
          <div class="flex items-start space-x-2">
            <span class="text-blue-400 mt-0.5">🛡️</span>
            <div>
              <h3 class="text-sm font-medium text-blue-400 mb-1">安全提示</h3>
              <p class="text-xs text-blue-200">
                管理后台仅限授权人员访问，请妥善保管您的登录凭据。
                如发现异常登录行为，请立即联系系统管理员。
              </p>
            </div>
          </div>
        </div>
      </div>

      <!-- 返回前台 -->
      <div class="text-center mt-6">
        <router-link
          to="/"
          class="inline-flex items-center text-gray-400 hover:text-gold-400 transition-colors text-sm"
        >
          <span class="mr-2">←</span>返回前台首页
        </router-link>
      </div>
    </div>

    <!-- 忘记密码弹窗 -->
    <div v-if="showForgotPassword" class="fixed inset-0 bg-black/50 flex items-center justify-center p-4 z-50">
      <div class="bg-gradient-to-br from-gray-800 to-gray-900 rounded-xl border border-gold-500/30 p-6 w-full max-w-md">
        <div class="text-center mb-4">
          <h3 class="text-xl font-bold text-white mb-2">忘记密码</h3>
          <p class="text-gray-400 text-sm">请联系系统管理员重置密码</p>
        </div>
        
        <div class="bg-yellow-600/20 border border-yellow-500/30 rounded-lg p-4 mb-4">
          <div class="flex items-start space-x-2">
            <span class="text-yellow-400">⚠️</span>
            <div>
              <p class="text-yellow-200 text-sm">
                出于安全考虑，管理员密码重置需要线下验证身份。
                请联系技术部门或系统管理员进行密码重置。
              </p>
            </div>
          </div>
        </div>

        <div class="space-y-3">
          <div class="flex items-center justify-between p-3 bg-gray-700/30 rounded-lg">
            <span class="text-gray-300 text-sm">技术支持</span>
            <span class="text-gold-400 text-sm font-medium">400-888-8888</span>
          </div>
          <div class="flex items-center justify-between p-3 bg-gray-700/30 rounded-lg">
            <span class="text-gray-300 text-sm">邮箱支持</span>
            <span class="text-gold-400 text-sm font-medium"><EMAIL></span>
          </div>
        </div>

        <div class="flex space-x-3 mt-6">
          <button
            @click="showForgotPassword = false"
            class="flex-1 py-2 px-4 bg-gray-600 hover:bg-gray-500 text-white rounded-lg transition-colors"
          >
            关闭
          </button>
          <button
            @click="copyContact"
            class="flex-1 py-2 px-4 bg-gold-600 hover:bg-gold-500 text-black font-medium rounded-lg transition-colors"
          >
            复制联系方式
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '../../stores/auth'
import { ElMessage } from 'element-plus'

const router = useRouter()
const authStore = useAuthStore()

// 响应式数据
const loading = ref(false)
const showPassword = ref(false)
const showForgotPassword = ref(false)

// 登录表单
const loginForm = ref({
  username: '',
  password: '',
  remember: false
})

// 表单验证
const isFormValid = computed(() => {
  return loginForm.value.username.trim() !== '' && 
         loginForm.value.password.trim() !== ''
})

// 处理登录
const handleLogin = async () => {
  if (!isFormValid.value) {
    ElMessage.error('请填写完整的登录信息')
    return
  }

  loading.value = true

  try {
    // 使用认证store的管理员登录方法
    const result = await authStore.adminLogin(loginForm.value.username, loginForm.value.password)

    if (result.success) {
      ElMessage.success('登录成功，欢迎回来！')

      // 立即跳转到管理后台
      router.push('/admin')
    } else {
      ElMessage.error(result.message || '登录失败')
    }
  } catch (error) {
    console.error('登录失败:', error)
    ElMessage.error('登录失败，请稍后重试')
  } finally {
    loading.value = false
  }
}

// 复制联系方式
const copyContact = () => {
  const contactInfo = '技术支持: 400-888-8888\n邮箱支持: <EMAIL>'
  
  if (navigator.clipboard) {
    navigator.clipboard.writeText(contactInfo).then(() => {
      ElMessage.success('联系方式已复制到剪贴板')
      showForgotPassword.value = false
    }).catch(() => {
      ElMessage.error('复制失败，请手动复制')
    })
  } else {
    ElMessage.error('浏览器不支持自动复制，请手动复制')
  }
}
</script>

<style scoped>
/* 自定义样式 */
.animate-spin {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
</style>
