import { ref, computed } from 'vue'
import { defineStore } from 'pinia'
import { userAPI, adminAPI } from '../api/config'

interface User {
  id: number
  platformId: string
  username: string
  status: string
  totalWinnings: number
}

interface Admin {
  id: number
  username: string
  email: string
  role: string
  status: string
  lastLoginAt?: string
  lastLoginIp?: string
}

export const useAuthStore = defineStore('auth', () => {
  // 状态
  const token = ref<string | null>(localStorage.getItem('token'))
  const adminToken = ref<string | null>(localStorage.getItem('adminToken'))
  const user = ref<User | null>(null)
  const admin = ref<Admin | null>(null)
  const isLoading = ref(false)

  // 监听localStorage变化，自动更新token状态
  if (typeof window !== 'undefined') {
    window.addEventListener('storage', (e) => {
      if (e.key === 'token') {
        token.value = e.newValue
        if (!e.newValue) {
          user.value = null
        }
      }
      if (e.key === 'adminToken') {
        adminToken.value = e.newValue
        if (!e.newValue) {
          admin.value = null
        }
      }
    })
  }

  // 计算属性
  const isAuthenticated = computed(() => !!token.value)
  const isAdminAuthenticated = computed(() => !!adminToken.value)
  const isAdmin = computed(() => !!admin.value)

  // 登录
  const login = async (username: string, password: string) => {
    isLoading.value = true
    try {
      const response = await userAPI.login({ username, password })

      if (response.data.success && response.data.data.token) {
        token.value = response.data.data.token
        user.value = response.data.data.user
        localStorage.setItem('token', response.data.data.token)
        localStorage.setItem('user', JSON.stringify(response.data.data.user))
        return { success: true, message: response.data.message }
      }

      return { success: false, message: response.data.error || '登录失败' }
    } catch (error: any) {
      console.error('登录错误:', error)
      return {
        success: false,
        message: error.response?.data?.error || '登录失败，请检查网络连接'
      }
    } finally {
      isLoading.value = false
    }
  }

  // 注册
  const register = async (platformId: string, username: string, password: string) => {
    isLoading.value = true
    try {
      const response = await userAPI.register({ platformId, username, password })

      if (response.data.success) {
        // 注册成功，返回成功信息（不自动登录）
        return { success: true, message: response.data.message, data: response.data.data }
      }

      return { success: false, message: response.data.error || '注册失败' }
    } catch (error: any) {
      console.error('注册错误:', error)
      return {
        success: false,
        message: error.response?.data?.error || '注册失败，请检查网络连接'
      }
    } finally {
      isLoading.value = false
    }
  }

  // 管理员登录
  const adminLogin = async (username: string, password: string) => {
    isLoading.value = true
    try {
      const response = await adminAPI.login({ username, password })

      if (response.data.success && response.data.data.token) {
        adminToken.value = response.data.data.token
        admin.value = response.data.data.admin
        localStorage.setItem('adminToken', response.data.data.token)
        localStorage.setItem('admin', JSON.stringify(response.data.data.admin))
        return { success: true, message: response.data.message }
      }

      return { success: false, message: response.data.error || '管理员登录失败' }
    } catch (error: any) {
      console.error('管理员登录错误:', error)
      return {
        success: false,
        message: error.response?.data?.error || '管理员登录失败，请检查网络连接'
      }
    } finally {
      isLoading.value = false
    }
  }

  // 用户登出
  const logout = () => {
    token.value = null
    user.value = null
    localStorage.removeItem('token')
    localStorage.removeItem('user')
  }

  // 管理员登出
  const adminLogout = async () => {
    try {
      if (adminToken.value) {
        await adminAPI.logout()
      }
    } catch (error) {
      console.error('管理员登出错误:', error)
    } finally {
      adminToken.value = null
      admin.value = null
      localStorage.removeItem('adminToken')
      localStorage.removeItem('admin')
    }
  }

  // 初始化认证信息（从localStorage恢复）
  const initializeAuth = () => {
    // 恢复用户信息
    const savedUser = localStorage.getItem('user')
    if (savedUser && token.value) {
      try {
        user.value = JSON.parse(savedUser)
      } catch (error) {
        console.error('解析用户信息失败:', error)
        logout()
      }
    }

    // 恢复管理员信息
    const savedAdmin = localStorage.getItem('admin')
    if (savedAdmin && adminToken.value) {
      try {
        admin.value = JSON.parse(savedAdmin)
      } catch (error) {
        console.error('解析管理员信息失败:', error)
        adminLogout()
      }
    }
  }

  // 获取用户状态（包括剩余抽奖次数等）
  const getUserStatus = async () => {
    if (!token.value) return null
    
    try {
      const response = await userAPI.getUserStatus()
      return response.data
    } catch (error) {
      console.error('获取用户状态失败:', error)
      return null
    }
  }

  return {
    // 状态
    token,
    adminToken,
    user,
    admin,
    isLoading,

    // 计算属性
    isAuthenticated,
    isAdminAuthenticated,
    isAdmin,

    // 方法
    login,
    register,
    adminLogin,
    logout,
    adminLogout,
    initializeAuth,
    getUserStatus
  }
})
