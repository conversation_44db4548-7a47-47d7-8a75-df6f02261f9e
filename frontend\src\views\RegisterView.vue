<script setup lang="ts">
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { useAuthStore } from '../stores/auth'

const router = useRouter()
const authStore = useAuthStore()

// 表单数据
const registerForm = ref({
  userId: '',
  username: '',
  password: '',
  confirmPassword: ''
})

const loading = ref(false)
const registerSuccess = ref(false)

// 表单验证
const validateForm = () => {
  if (!registerForm.value.userId) {
    ElMessage.error('请输入您在平台的账号ID')
    return false
  }
  if (registerForm.value.userId.length < 3 || registerForm.value.userId.length > 20) {
    ElMessage.error('平台账号ID长度在 3 到 20 个字符')
    return false
  }
  if (!registerForm.value.username) {
    ElMessage.error('请输入登录账号')
    return false
  }
  if (registerForm.value.username.length < 3 || registerForm.value.username.length > 20) {
    ElMessage.error('登录账号长度在 3 到 20 个字符')
    return false
  }
  if (!registerForm.value.password) {
    ElMessage.error('请输入密码')
    return false
  }
  if (registerForm.value.password.length < 6 || registerForm.value.password.length > 20) {
    ElMessage.error('密码长度在 6 到 20 个字符')
    return false
  }
  if (registerForm.value.password !== registerForm.value.confirmPassword) {
    ElMessage.error('两次输入的密码不一致')
    return false
  }
  return true
}

// 注册处理
const handleRegister = async () => {
  if (!validateForm()) return

  loading.value = true

  try {
    const result = await authStore.register(
      registerForm.value.userId,
      registerForm.value.username,
      registerForm.value.password
    )

    if (result.success) {
      registerSuccess.value = true
      ElMessage.success({
        message: `注册成功！用户名：${registerForm.value.username}，请前往登录页面登录`,
        duration: 3000
      })

      // 跳转到登录页面
      setTimeout(() => {
        router.push('/login')
      }, 2000)
    } else {
      ElMessage.error(result.message || '注册失败，请重试')
    }

  } catch (error: any) {
    ElMessage.error(error.message || '注册失败，请重试')
  } finally {
    loading.value = false
  }
}

// 跳转到登录页面
const goToLogin = () => {
  router.push('/login')
}

// 返回首页
const goHome = () => {
  router.push('/')
}
</script>

<template>
  <div class="min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-black flex items-center justify-center p-4">
    <div class="w-full max-w-md">
      <!-- 返回按钮 -->
      <div class="mb-6">
        <button @click="goHome" 
                class="flex items-center text-gold-400 hover:text-gold-300 transition-colors">
          <span class="mr-2">←</span>
          返回首页
        </button>
      </div>

      <!-- 注册卡片 -->
      <div class="bg-gray-800 rounded-2xl p-8 shadow-2xl border border-gray-700">
        <!-- 标题 -->
        <div class="text-center mb-8">
          <div class="text-4xl mb-4">🎰</div>
          <h1 class="text-2xl md:text-3xl font-bold text-gold-400 mb-2">抽奖活动注册</h1>
          <p class="text-gray-400">请填写您在平台的账号ID参与抽奖</p>
        </div>

        <!-- 注册成功提示 -->
        <div v-if="registerSuccess" class="mb-6 p-4 bg-green-900/50 border border-green-500 rounded-lg">
          <div class="flex items-center">
            <div class="text-green-400 text-xl mr-3">✅</div>
            <div>
              <p class="text-green-400 font-semibold">注册成功！</p>
              <p class="text-green-300 text-sm">正在跳转到登录页面...</p>
            </div>
          </div>
        </div>

        <!-- 注册表单 -->
        <form @submit.prevent="handleRegister" class="space-y-6" v-if="!registerSuccess">
          <!-- 平台账号ID -->
          <div>
            <label class="block text-sm font-medium text-gray-300 mb-2">平台账号ID</label>
            <input
              v-model="registerForm.userId"
              type="text"
              placeholder="请输入您在平台的账号ID"
              class="w-full px-4 py-3 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-gold-500 focus:border-transparent transition-all"
              :disabled="loading"
            />
            <p class="text-xs text-gray-400 mt-1">请填写您在原平台注册的账号ID，用于抽奖身份验证</p>
          </div>

          <!-- 登录账号 -->
          <div>
            <label class="block text-sm font-medium text-gray-300 mb-2">登录账号</label>
            <input
              v-model="registerForm.username"
              type="text"
              placeholder="请设置登录账号（用于登录抽奖系统）"
              class="w-full px-4 py-3 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-gold-500 focus:border-transparent transition-all"
              :disabled="loading"
            />
          </div>

          <!-- 密码输入 -->
          <div>
            <label class="block text-sm font-medium text-gray-300 mb-2">密码</label>
            <input 
              v-model="registerForm.password"
              type="password" 
              placeholder="请输入密码（6-20位）"
              class="w-full px-4 py-3 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-gold-500 focus:border-transparent transition-all"
              :disabled="loading"
            />
          </div>

          <!-- 确认密码 -->
          <div>
            <label class="block text-sm font-medium text-gray-300 mb-2">确认密码</label>
            <input 
              v-model="registerForm.confirmPassword"
              type="password" 
              placeholder="请再次输入密码"
              class="w-full px-4 py-3 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-gold-500 focus:border-transparent transition-all"
              :disabled="loading"
            />
          </div>

          <!-- 注册按钮 -->
          <button 
            type="submit"
            :disabled="loading"
            class="w-full py-3 bg-gradient-to-r from-gold-500 to-gold-600 hover:from-gold-600 hover:to-gold-700 disabled:from-gray-600 disabled:to-gray-700 text-black font-bold rounded-lg transition-all duration-300 transform hover:scale-105 disabled:transform-none disabled:cursor-not-allowed"
          >
            <span v-if="loading" class="flex items-center justify-center">
              <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-black" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              注册中...
            </span>
            <span v-else>🎯 立即注册</span>
          </button>
        </form>

        <!-- 分割线和登录链接 -->
        <div v-if="!registerSuccess">
          <!-- 分割线 -->
          <div class="my-6 flex items-center">
            <div class="flex-1 border-t border-gray-600"></div>
            <span class="px-4 text-gray-400 text-sm">或</span>
            <div class="flex-1 border-t border-gray-600"></div>
          </div>

          <!-- 登录链接 -->
          <div class="text-center">
            <p class="text-gray-400 mb-4">已有账号？</p>
            <button @click="goToLogin"
                    class="w-full py-3 bg-gray-700 hover:bg-gray-600 text-white font-semibold rounded-lg transition-colors border border-gray-600">
              立即登录
            </button>
          </div>
        </div>

        <!-- 提示信息 -->
        <div class="mt-6 p-4 bg-green-900/30 border border-green-700 rounded-lg">
          <div class="flex items-start">
            <span class="text-green-400 mr-2">✅</span>
            <div class="text-sm text-green-300">
              <p class="font-semibold mb-1">注册须知：</p>
              <ul class="space-y-1">
                <li>• 平台账号ID必须是您在原平台的真实ID</li>
                <li>• 登录账号用于登录抽奖系统，请设置易记的用户名</li>
                <li>• 密码请妥善保管，建议包含数字和字母</li>
                <li>• 确保信息准确，以便中奖后能正确发放奖品</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
