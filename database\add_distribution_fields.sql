-- 为lottery_records表添加发放相关字段
-- 执行此脚本来添加奖品发放管理功能所需的字段

USE as_chou;

-- 1. 添加发放时间字段
ALTER TABLE lottery_records 
ADD COLUMN distributed_at DATETIME NULL COMMENT '发放时间' AFTER status;

-- 2. 添加发放人字段
ALTER TABLE lottery_records 
ADD COLUMN distributed_by BIGINT NULL COMMENT '发放人ID(管理员ID)' AFTER distributed_at;

-- 3. 添加发放备注字段
ALTER TABLE lottery_records 
ADD COLUMN distribution_remark TEXT NULL COMMENT '发放备注' AFTER distributed_by;

-- 4. 添加外键约束（可选，如果需要严格的数据完整性）
-- ALTER TABLE lottery_records 
-- ADD CONSTRAINT fk_lottery_records_distributed_by 
-- FOREIGN KEY (distributed_by) REFERENCES admins(id) ON DELETE SET NULL;

-- 5. 添加索引优化查询性能
CREATE INDEX idx_lottery_records_status ON lottery_records(status);
CREATE INDEX idx_lottery_records_distributed_at ON lottery_records(distributed_at);
CREATE INDEX idx_lottery_records_distributed_by ON lottery_records(distributed_by);

-- 6. 验证字段添加结果
DESCRIBE lottery_records;

-- 7. 显示当前记录状态分布
SELECT 
    status,
    COUNT(*) as count,
    CONCAT(ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM lottery_records), 2), '%') as percentage
FROM lottery_records 
GROUP BY status
ORDER BY count DESC;

SELECT '发放字段添加完成！' as result;
