# 抽奖接口开发文档

## 📋 项目概述

本文档描述了抽奖系统的接口开发需求，包括后端API优化和前端Lucky Canvas转盘集成。

### 核心功能
- 支持个人中奖率配置
- 严格的时间和次数验证
- 并发安全的抽奖逻辑
- 前端转盘动画集成

## 🗄️ 数据库表结构

### 相关表说明

#### 1. lottery_periods (抽奖期次表)
```sql
- id: 期次ID
- period_name: 期次名称
- status: 状态 ('未开始','进行中','已结束','已开奖')
- start_time: 开始时间
- end_time: 结束时间
- max_attempts_per_user: 每用户最大抽奖次数
- total_prize_amount: 总奖金(仅显示用)
- actual_drawable_amount: 实际可抽金额(暂不使用)
- total_shares: 总份数(暂不使用)
```

#### 2. prize_configs (奖品配置表)
```sql
- id: 奖品配置ID
- period_id: 期次ID
- prize_name: 奖品名称
- prize_amount: 奖品金额
- probability: 中奖概率(全局)
- is_active: 是否启用
```

#### 3. user_prize_configs (用户个人抽奖率配置表)
```sql
- id: 配置ID
- user_id: 用户ID
- prize_configs: JSON格式的个人概率配置
- total_probability: 总概率(必须为1.0000)
- is_enabled: 是否启用个人设置
```

JSON结构示例：
```json
{
  "configs": [
    {
      "prizeConfigId": 1,
      "prizeName": "特等奖",
      "prizeAmount": 5000.00,
      "globalProbability": 0.0010,
      "customProbability": 0.0005,
      "isEnabled": true
    }
  ]
}
```

#### 4. users (用户表)
```sql
- id: 用户ID
- custom_lottery_enabled: 个人抽奖率总开关
```

#### 5. lottery_records (抽奖记录表)
```sql
- id: 记录ID
- period_id: 期次ID
- user_id: 用户ID
- prize_name: 奖品名称
- prize_amount: 奖品金额
- is_winner: 是否中奖
- draw_result: JSON格式的抽奖详情
- status: 状态 ('待发放','已发放','已取消')
```

## 🔧 后端开发任务

### 第一阶段：抽奖接口优化

#### 1.1 修改现有抽奖接口
文件：`backend/src/routes/lottery.js`
接口：`POST /api/lottery/draw`

**新增验证逻辑：**

1. **时间验证**
```javascript
function validateLotteryTime(period) {
  const now = new Date()
  const startTime = new Date(period.startTime)
  const endTime = new Date(period.endTime)
  
  if (now < startTime) {
    return { valid: false, message: '抽奖活动尚未开始' }
  }
  
  if (now > endTime) {
    return { valid: false, message: '抽奖活动已结束' }
  }
  
  return { valid: true }
}
```

2. **用户抽奖次数验证**
```javascript
async function validateUserAttempts(userId, periodId, maxAttempts, transaction) {
  // 从抽奖记录表统计已使用次数
  const usedAttempts = await LotteryRecord.count({
    where: { userId, periodId },
    transaction
  })
  
  const remainingAttempts = maxAttempts - usedAttempts
  
  if (remainingAttempts <= 0) {
    return { 
      valid: false, 
      message: '本期抽奖次数已用完',
      remainingAttempts: 0,
      usedAttempts
    }
  }
  
  return { valid: true, remainingAttempts, usedAttempts }
}
```

3. **个人中奖率配置获取**
```javascript
async function getUserEffectivePrizeConfigs(userId, periodId, transaction) {
  // 1. 检查用户是否启用个人抽奖率
  const user = await User.findByPk(userId, { transaction })
  
  if (user.customLotteryEnabled) {
    // 2. 获取用户个人配置
    const userPrizeConfig = await UserPrizeConfig.findOne({
      where: { userId },
      transaction
    })
    
    if (userPrizeConfig && userPrizeConfig.isEnabled) {
      // 3. 验证总概率是否为100%
      if (Math.abs(userPrizeConfig.totalProbability - 1.0) < 0.0001) {
        return {
          configs: userPrizeConfig.prizeConfigs.configs,
          source: 'personal',
          totalProbability: userPrizeConfig.totalProbability
        }
      }
    }
  }
  
  // 4. 使用全局配置
  const globalConfigs = await PrizeConfig.findAll({
    where: { periodId, isActive: true },
    order: [['sortOrder', 'ASC']],
    transaction
  })
  
  return {
    configs: globalConfigs.map(config => ({
      prizeConfigId: config.id,
      prizeName: config.prizeName,
      prizeAmount: parseFloat(config.prizeAmount),
      customProbability: parseFloat(config.probability),
      isEnabled: true
    })),
    source: 'global',
    totalProbability: globalConfigs.reduce((sum, config) => 
      sum + parseFloat(config.probability), 0)
  }
}
```

#### 1.2 并发安全的抽奖接口
```javascript
router.post('/draw', authenticateToken, async (req, res) => {
  const transaction = await sequelize.transaction({
    isolationLevel: Transaction.ISOLATION_LEVELS.READ_COMMITTED
  })
  
  try {
    const { periodId } = req.body
    const userId = req.user.userId
    
    // 1. 验证期次（加行级锁）
    const period = await LotteryPeriod.findByPk(periodId, { 
      transaction,
      lock: true
    })
    
    if (!period || period.status !== '进行中') {
      await transaction.rollback()
      return res.status(400).json({
        success: false,
        error: '当前期次不可用或已结束'
      })
    }
    
    // 2. 验证时间
    const timeValidation = validateLotteryTime(period)
    if (!timeValidation.valid) {
      await transaction.rollback()
      return res.status(400).json({
        success: false,
        error: timeValidation.message
      })
    }
    
    // 3. 验证用户抽奖次数
    const attemptsValidation = await validateUserAttempts(
      userId, periodId, period.maxAttemptsPerUser, transaction
    )
    
    if (!attemptsValidation.valid) {
      await transaction.rollback()
      return res.status(400).json({
        success: false,
        error: attemptsValidation.message,
        data: {
          remainingAttempts: attemptsValidation.remainingAttempts,
          usedAttempts: attemptsValidation.usedAttempts
        }
      })
    }
    
    // 4. 获取有效概率配置
    const prizeConfigResult = await getUserEffectivePrizeConfigs(
      userId, periodId, transaction
    )
    
    if (!prizeConfigResult.configs.length) {
      await transaction.rollback()
      return res.status(400).json({
        success: false,
        error: '当前期次没有可用奖品配置'
      })
    }
    
    // 5. 执行抽奖算法
    const drawResult = performPersonalizedLotteryDraw(
      prizeConfigResult.configs, userId
    )
    
    // 6. 创建抽奖记录
    const user = await User.findByPk(userId, { transaction })
    const record = await LotteryRecord.create({
      periodId,
      userId,
      platformId: user.platformId,
      prizeName: drawResult.prizeName,
      prizeAmount: drawResult.prizeAmount,
      isWinner: drawResult.isWinner,
      drawResult: {
        configSource: prizeConfigResult.source,
        usedConfigs: prizeConfigResult.configs,
        winningPrize: drawResult,
        drawTime: new Date()
      },
      status: drawResult.isWinner ? '待发放' : '已发放'
    }, { transaction })
    
    await transaction.commit()
    
    // 7. 返回结果
    res.json({
      success: true,
      message: drawResult.isWinner ? '恭喜中奖！' : '谢谢参与！',
      data: {
        record: {
          id: record.id,
          prizeName: record.prizeName,
          prizeAmount: parseFloat(record.prizeAmount),
          isWinner: record.isWinner,
          drawnAt: record.drawnAt
        },
        remainingAttempts: attemptsValidation.remainingAttempts - 1,
        configSource: prizeConfigResult.source
      }
    })
    
  } catch (error) {
    await transaction.rollback()
    console.error('抽奖执行错误:', error)
    res.status(500).json({
      success: false,
      error: '抽奖失败，请稍后重试'
    })
  }
})
```

#### 1.3 个人化抽奖算法
```javascript
function performPersonalizedLotteryDraw(prizeConfigs, userId) {
  // 计算总概率
  const totalProbability = prizeConfigs.reduce((sum, config) => 
    sum + parseFloat(config.customProbability), 0)
  
  // 生成随机数
  const random = Math.random() * totalProbability
  
  // 确定中奖奖品
  let currentProbability = 0
  for (const config of prizeConfigs) {
    currentProbability += parseFloat(config.customProbability)
    if (random <= currentProbability) {
      return {
        prizeName: config.prizeName,
        prizeAmount: parseFloat(config.prizeAmount),
        isWinner: config.prizeAmount > 0,
        probability: config.customProbability,
        configId: config.prizeConfigId
      }
    }
  }
  
  // 兜底返回最后一个奖品
  const lastConfig = prizeConfigs[prizeConfigs.length - 1]
  return {
    prizeName: lastConfig.prizeName,
    prizeAmount: parseFloat(lastConfig.prizeAmount),
    isWinner: lastConfig.prizeAmount > 0,
    probability: lastConfig.customProbability,
    configId: lastConfig.prizeConfigId
  }
}
```

## 🎨 前端开发任务

### 第二阶段：Lucky Canvas集成

#### 2.1 修改抽奖逻辑
文件：`frontend/src/views/LotteryView.vue`

**修改 startCallback 函数：**
```javascript
const startCallback = async () => {
  // 前置验证保持不变
  if (!authStore.isAuthenticated) {
    ElMessage.warning('请先登录后参与抽奖')
    return
  }

  if (isSpinning.value) {
    ElMessage.warning('抽奖进行中，请稍候')
    return
  }

  if (!userStatus.value || userStatus.value.remainingAttempts <= 0) {
    ElMessage.warning('本期抽奖次数已用完')
    return
  }

  if (!currentPeriod.value || currentPeriod.value.status !== '进行中') {
    ElMessage.warning('当前期次未开始或已结束')
    return
  }

  isSpinning.value = true
  myLucky.value?.play()

  try {
    // 调用真实抽奖API
    const response = await userAPI.drawLottery(currentPeriod.value.id)

    if (response.data.success) {
      const result = response.data.data

      // 根据奖品名称找到转盘索引
      const prizeIndex = findPrizeIndexByName(result.record.prizeName)

      // 延迟停止转盘
      setTimeout(() => {
        myLucky.value?.stop(prizeIndex)
      }, 2000)

      // 更新用户状态
      if (userStatus.value) {
        userStatus.value.remainingAttempts = result.remainingAttempts
      }

    } else {
      // 处理抽奖失败
      myLucky.value?.stop(0)
      ElMessage.error(response.data.error)
      isSpinning.value = false
    }

  } catch (error) {
    console.error('抽奖请求失败:', error)
    myLucky.value?.stop(0)
    ElMessage.error('抽奖失败，请稍后重试')
    isSpinning.value = false
  }
}

// 根据奖品名称查找转盘索引
function findPrizeIndexByName(prizeName) {
  const index = luckyPrizes.value.findIndex(prize => 
    prize.fonts && prize.fonts[0] && prize.fonts[0].text === prizeName
  )
  return index >= 0 ? index : 0
}
```

#### 2.2 修改 endCallback 函数
```javascript
const endCallback = (prize) => {
  isSpinning.value = false
  
  // 显示中奖结果（基于真实API返回的数据）
  if (lotteryResult.value) {
    if (lotteryResult.value.isWinner) {
      ElMessage.success(`恭喜您获得${lotteryResult.value.prizeName}！`)
    } else {
      ElMessage.info('谢谢参与，下次再来！')
    }
  }
}
```

## 📝 开发注意事项

### 不需要实现的功能
- ❌ prize_level 字段处理
- ❌ 奖品库存限制检查
- ❌ actual_drawable_amount 和 total_shares 业务逻辑
- ❌ 中奖公告实时更新
- ❌ 复杂的概率控制机制

### 需要保持的现有逻辑
- ✅ 真随机抽奖算法
- ✅ Lucky Canvas 转盘配置
- ✅ 用户认证和状态管理
- ✅ 现有UI设计

### 关键技术点
1. **时间验证**：严格按服务器时区，超时即失败
2. **次数计算**：从 lottery_records 表实时统计
3. **概率配置**：优先个人设置，否则全局设置
4. **并发安全**：使用数据库事务和行级锁
5. **错误处理**：详细的错误分类和用户提示

## 🧪 测试计划

### 后端测试
1. 时间边界测试（开始前、进行中、结束后）
2. 次数限制测试（正常、边界、超限）
3. 个人概率配置测试（启用/禁用、有效/无效）
4. 并发抽奖测试
5. 异常情况测试

### 前端测试
1. 转盘停止位置准确性
2. 用户状态更新
3. 错误提示显示
4. 网络异常处理

## 📋 开发检查清单

### 后端开发
- [ ] 时间验证函数实现
- [ ] 用户次数验证函数实现
- [ ] 个人概率配置获取函数实现
- [ ] 并发安全的抽奖接口实现
- [ ] 个人化抽奖算法实现
- [ ] 错误处理和日志记录
- [ ] 数据库事务处理

### 前端开发
- [ ] startCallback 函数修改
- [ ] endCallback 函数修改
- [ ] findPrizeIndexByName 函数实现
- [ ] 错误处理和用户提示
- [ ] 用户状态更新逻辑

### 测试验证
- [ ] 后端API单元测试
- [ ] 前端集成测试
- [ ] 端到端测试
- [ ] 性能和并发测试

---

**文档版本**: v1.0  
**创建时间**: 2025-07-28  
**最后更新**: 2025-07-28
