const { DataTypes } = require('sequelize')
const { sequelize } = require('../config/database')

// 奖品配置模型
const PrizeConfig = sequelize.define('PrizeConfig', {
  id: {
    type: DataTypes.BIGINT,
    primaryKey: true,
    autoIncrement: true
  },
  periodId: {
    type: DataTypes.BIGINT,
    allowNull: false,
    field: 'period_id'
  },
  prizeLevel: {
    type: DataTypes.INTEGER,
    allowNull: false,
    field: 'prize_level'
  },
  prizeName: {
    type: DataTypes.STRING(100),
    allowNull: false,
    field: 'prize_name'
  },
  prizeAmount: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: false,
    field: 'prize_amount'
  },
  probability: {
    type: DataTypes.DECIMAL(5, 4),
    allowNull: false
  },
  maxWinners: {
    type: DataTypes.INTEGER,
    defaultValue: -1,
    field: 'max_winners'
  },
  currentWinners: {
    type: DataTypes.INTEGER,
    defaultValue: 0,
    field: 'current_winners'
  },
  colorClass: {
    type: DataTypes.STRING(100),
    field: 'color_class'
  },
  sortOrder: {
    type: DataTypes.INTEGER,
    defaultValue: 0,
    field: 'sort_order'
  },
  isActive: {
    type: DataTypes.BOOLEAN,
    defaultValue: true,
    field: 'is_active'
  }
}, {
  tableName: 'prize_configs',
  underscored: true
})

module.exports = PrizeConfig
