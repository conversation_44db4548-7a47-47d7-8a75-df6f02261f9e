<template>
  <div class="admin-lottery-records min-h-screen bg-gradient-to-br from-gray-900 via-black to-gray-800 p-6">
    <!-- 页面头部 -->
    <div class="mb-8">
      <div class="flex items-center justify-between">
        <div>
          <h2 class="text-3xl font-bold text-white mb-2 flex items-center">
            <el-icon class="mr-3 text-gold-400" size="32"><List /></el-icon>
            抽奖记录
          </h2>
          <p class="text-gray-400">查看和管理所有抽奖记录</p>
        </div>
        <div class="flex items-center space-x-4">
          <div class="px-4 py-2 bg-gradient-to-r from-gold-500/20 to-gold-600/20 border border-gold-500/30 rounded-lg">
            <span class="text-gold-400 text-sm font-medium">总记录数: {{ filteredRecords.length }}</span>
          </div>
          <button
            @click="handleRefresh"
            class="px-4 py-2 bg-blue-500/20 hover:bg-blue-500/30 border border-blue-500/30 text-blue-400 hover:text-blue-300 rounded-lg transition-all duration-200 flex items-center space-x-2"
          >
            <el-icon><Refresh /></el-icon>
            <span>刷新数据</span>
          </button>
        </div>
      </div>
    </div>

    <!-- 搜索和筛选 -->
    <div class="bg-gradient-to-br from-gray-800 to-gray-900 border border-gold-500/30 rounded-xl shadow-xl p-6 mb-6">
      <div class="flex flex-wrap gap-4 items-center">
        <div class="flex-1 min-w-[300px]">
          <input
            v-model="searchKeyword"
            placeholder="搜索用户名或平台ID..."
            class="w-full px-4 py-3 bg-gray-700 border border-gray-600 text-white placeholder-gray-400 rounded-lg focus:outline-none focus:border-gold-500 focus:ring-1 focus:ring-gold-500 transition-all"
          />
        </div>

        <select
          v-model="winnerFilter"
          class="px-4 py-3 bg-gray-700 border border-gray-600 text-white rounded-lg focus:outline-none focus:border-gold-500 min-w-[150px]"
        >
          <option value="">全部中奖状态</option>
          <option value="true">中奖</option>
          <option value="false">未中奖</option>
        </select>

        <button
          @click="handleSearch"
          class="px-6 py-3 bg-gradient-to-r from-gold-500 to-gold-600 hover:from-gold-600 hover:to-gold-700 text-black font-medium rounded-lg transition-all duration-200 flex items-center space-x-2"
        >
          <el-icon><Search /></el-icon>
          <span>搜索</span>
        </button>
      </div>
    </div>

    <!-- 抽奖记录表格 -->
    <div class="bg-gradient-to-br from-gray-800 to-gray-900 border border-gold-500/30 rounded-xl shadow-xl overflow-hidden">
      <!-- 表格头部 -->
      <div class="px-6 py-4 border-b border-gray-700">
        <h3 class="text-lg font-semibold text-white flex items-center">
          <el-icon class="mr-2 text-gold-400"><Document /></el-icon>
          抽奖记录列表
        </h3>
      </div>

      <!-- 加载状态 -->
      <div v-if="isLoading" class="flex items-center justify-center py-12">
        <div class="text-center">
          <div class="w-8 h-8 border-2 border-gold-500 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p class="text-gray-400">加载中...</p>
        </div>
      </div>

      <!-- 表格内容 -->
      <div v-else-if="paginatedRecords.length > 0" class="overflow-x-auto">
        <table class="w-full">
          <thead class="bg-gray-700/50">
            <tr>
              <th class="px-6 py-4 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">记录ID</th>
              <th class="px-6 py-4 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">用户信息</th>
              <th class="px-6 py-4 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">期次</th>
              <th class="px-6 py-4 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">中奖状态</th>
              <th class="px-6 py-4 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">奖品信息</th>
              <th class="px-6 py-4 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">发放状态</th>
              <th class="px-6 py-4 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">抽奖时间</th>
              <th class="px-6 py-4 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">操作</th>
            </tr>
          </thead>
          <tbody class="divide-y divide-gray-700">
            <tr
              v-for="record in paginatedRecords"
              :key="record.id"
              class="hover:bg-gray-700/30 transition-colors"
            >
              <!-- 记录ID -->
              <td class="px-6 py-4 whitespace-nowrap">
                <span class="text-gold-400 font-medium">#{{ record.id }}</span>
              </td>

              <!-- 用户信息 -->
              <td class="px-6 py-4 whitespace-nowrap">
                <div>
                  <div class="text-white font-medium">{{ record.user?.username || '未知用户' }}</div>
                  <div class="text-gray-400 text-sm">ID: {{ record.user?.platformId || record.platformId }}</div>
                </div>
              </td>

              <!-- 期次 -->
              <td class="px-6 py-4 whitespace-nowrap">
                <span class="px-2 py-1 bg-blue-500/20 border border-blue-500/30 text-blue-400 rounded text-sm">
                  {{ record.period?.periodName || `期次${record.periodId}` }}
                </span>
              </td>

              <!-- 中奖状态 -->
              <td class="px-6 py-4 whitespace-nowrap">
                <span :class="record.isWinner ? 'bg-green-500/20 border-green-500/30 text-green-400' : 'bg-gray-500/20 border-gray-500/30 text-gray-400'" class="px-2 py-1 border rounded text-sm font-medium">
                  {{ record.isWinner ? '中奖' : '未中奖' }}
                </span>
              </td>

              <!-- 奖品信息 -->
              <td class="px-6 py-4 whitespace-nowrap">
                <div v-if="record.isWinner">
                  <div class="text-green-400 font-medium">{{ record.prizeName }}</div>
                  <div class="text-green-300 text-sm">¥{{ formatAmount(record.prizeAmount) }}</div>
                </div>
                <div v-else class="text-gray-500">
                  未中奖
                </div>
              </td>

              <!-- 发放状态 -->
              <td class="px-6 py-4 whitespace-nowrap">
                <div v-if="record.isWinner">
                  <span
                    v-if="record.status === '已发放'"
                    class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-500/20 text-green-400 border border-green-500/30"
                  >
                    已发放
                  </span>
                  <span
                    v-else-if="record.status === '待发放'"
                    class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-500/20 text-yellow-400 border border-yellow-500/30"
                  >
                    待发放
                  </span>
                  <span
                    v-else-if="record.status === '已取消'"
                    class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-500/20 text-red-400 border border-red-500/30"
                  >
                    已取消
                  </span>
                </div>
                <div v-else class="text-gray-500 text-sm">
                  -
                </div>
              </td>

              <!-- 抽奖时间 -->
              <td class="px-6 py-4 whitespace-nowrap text-gray-300 text-sm">
                {{ formatDate(record.drawnAt) }}
              </td>

              <!-- 操作 -->
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="flex items-center space-x-2">
                  <button
                    @click="handleViewDetails(record)"
                    class="px-3 py-1 bg-blue-500/20 hover:bg-blue-500/30 border border-blue-500/30 text-blue-400 hover:text-blue-300 rounded transition-all text-sm"
                  >
                    查看详情
                  </button>
                  <button
                    v-if="record.isWinner && record.status === '待发放'"
                    @click="handleDistribute(record)"
                    class="px-3 py-1 bg-green-500/20 hover:bg-green-500/30 border border-green-500/30 text-green-400 hover:text-green-300 rounded transition-all text-sm"
                  >
                    发放
                  </button>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      <!-- 空状态 -->
      <div v-else class="flex items-center justify-center py-12">
        <div class="text-center">
          <el-icon size="64" class="text-gray-600 mb-4"><Document /></el-icon>
          <p class="text-gray-400 text-lg mb-2">暂无抽奖记录</p>
          <p class="text-gray-500 text-sm">请检查搜索条件或稍后重试</p>
        </div>
      </div>

      <!-- 分页 -->
      <div v-if="filteredRecords.length > 0" class="px-6 py-4 border-t border-gray-700 flex justify-between items-center">
        <div class="text-gray-400 text-sm">
          共 {{ filteredRecords.length }} 条记录，每页显示 {{ pageSize }} 条
        </div>
        <div class="flex items-center space-x-2">
          <button
            @click="currentPage = Math.max(1, currentPage - 1)"
            :disabled="currentPage === 1"
            class="px-3 py-1 bg-gray-700 hover:bg-gray-600 disabled:bg-gray-800 disabled:text-gray-600 text-gray-300 rounded transition-all"
          >
            上一页
          </button>
          <span class="px-3 py-1 bg-gold-500/20 border border-gold-500/30 text-gold-400 rounded">
            {{ currentPage }} / {{ totalPages }}
          </span>
          <button
            @click="currentPage = Math.min(totalPages, currentPage + 1)"
            :disabled="currentPage === totalPages"
            class="px-3 py-1 bg-gray-700 hover:bg-gray-600 disabled:bg-gray-800 disabled:text-gray-600 text-gray-300 rounded transition-all"
          >
            下一页
          </button>
        </div>
      </div>
    </div>

    <!-- 记录详情对话框 -->
    <div v-if="detailDialogVisible" class="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
      <div class="bg-gradient-to-br from-gray-800 to-gray-900 border border-gold-500/30 rounded-xl shadow-2xl w-full max-w-2xl max-h-[90vh] overflow-y-auto">
        <!-- 对话框头部 -->
        <div class="flex items-center justify-between p-6 border-b border-gray-700">
          <h3 class="text-xl font-semibold text-white flex items-center">
            <el-icon class="mr-3 text-gold-400" size="24"><Document /></el-icon>
            抽奖记录详情
          </h3>
          <button
            @click="detailDialogVisible = false"
            class="p-2 text-gray-400 hover:text-white hover:bg-gray-700 rounded-lg transition-all"
          >
            <el-icon size="20"><Close /></el-icon>
          </button>
        </div>

        <!-- 详情内容 -->
        <div v-if="selectedRecord" class="p-6">
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div class="space-y-4">
              <div>
                <label class="block text-sm font-medium text-gray-400 mb-2">记录ID</label>
                <p class="text-white font-medium">#{{ selectedRecord.id }}</p>
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-400 mb-2">用户名</label>
                <p class="text-white font-medium">{{ selectedRecord.user?.username || '未知用户' }}</p>
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-400 mb-2">平台ID</label>
                <p class="text-blue-400 font-medium">{{ selectedRecord.platformId }}</p>
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-400 mb-2">期次</label>
                <p class="text-blue-400 font-medium">{{ selectedRecord.period?.periodName || `期次${selectedRecord.periodId}` }}</p>
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-400 mb-2">抽奖时间</label>
                <p class="text-gray-300">{{ formatDate(selectedRecord.drawnAt) }}</p>
              </div>
            </div>

            <div class="space-y-4">
              <div>
                <label class="block text-sm font-medium text-gray-400 mb-2">中奖状态</label>
                <span :class="selectedRecord.isWinner ? 'bg-green-500/20 border-green-500/30 text-green-400' : 'bg-gray-500/20 border-gray-500/30 text-gray-400'" class="px-3 py-1 border rounded-full text-sm font-medium">
                  {{ selectedRecord.isWinner ? '中奖' : '未中奖' }}
                </span>
              </div>

              <div v-if="selectedRecord.isWinner">
                <label class="block text-sm font-medium text-gray-400 mb-2">奖品名称</label>
                <p class="text-green-400 font-medium">{{ selectedRecord.prizeName }}</p>
              </div>

              <div v-if="selectedRecord.isWinner">
                <label class="block text-sm font-medium text-gray-400 mb-2">奖品金额</label>
                <p class="text-green-400 font-medium text-lg">¥{{ formatAmount(selectedRecord.prizeAmount) }}</p>
              </div>
            </div>
          </div>

          <div v-if="selectedRecord.drawResult" class="mt-6">
            <label class="block text-sm font-medium text-gray-400 mb-2">抽奖详情</label>
            <pre class="bg-gray-700 border border-gray-600 p-4 rounded-lg text-sm text-gray-300 overflow-x-auto">{{ formatDrawResult(selectedRecord.drawResult) }}</pre>
          </div>


        </div>
      </div>
    </div>
  </div>

  <!-- 发放奖品对话框 -->
  <div
    v-if="distributeDialogVisible"
    class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"
    @click.self="distributeDialogVisible = false"
  >
    <div class="bg-gradient-to-br from-gray-800 to-gray-900 border border-gold-500/30 rounded-xl shadow-2xl p-6 w-full max-w-md mx-4">
      <div class="flex items-center justify-between mb-6">
        <h3 class="text-xl font-bold text-white flex items-center">
          <el-icon class="mr-2 text-green-400" size="24"><Money /></el-icon>
          发放奖品
        </h3>
        <button
          @click="distributeDialogVisible = false"
          class="text-gray-400 hover:text-white transition-colors"
        >
          <el-icon size="20"><Close /></el-icon>
        </button>
      </div>

      <div v-if="distributeRecord" class="space-y-4">
        <div class="bg-gray-700/50 border border-gray-600 rounded-lg p-4">
          <div class="text-sm text-gray-400 mb-1">奖品信息</div>
          <div class="text-white font-medium">{{ distributeRecord.prizeName }}</div>
          <div class="text-gold-400 font-bold text-lg">¥{{ formatAmount(distributeRecord.prizeAmount) }}</div>
        </div>

        <div class="bg-gray-700/50 border border-gray-600 rounded-lg p-4">
          <div class="text-sm text-gray-400 mb-1">获奖用户</div>
          <div class="text-white font-medium">{{ distributeRecord.user?.username || '未知用户' }}</div>
          <div class="text-blue-400">{{ distributeRecord.platformId }}</div>
        </div>

        <div>
          <label class="block text-sm font-medium text-gray-400 mb-2">发放备注（可选）</label>
          <textarea
            v-model="distributeRemark"
            placeholder="请输入发放备注..."
            class="w-full px-3 py-2 bg-gray-700 border border-gray-600 text-white placeholder-gray-400 rounded-lg focus:outline-none focus:border-gold-500 focus:ring-1 focus:ring-gold-500 transition-all resize-none"
            rows="3"
          ></textarea>
        </div>

        <div class="flex justify-end space-x-3 pt-4">
          <button
            @click="distributeDialogVisible = false"
            class="px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white rounded-lg transition-all"
            :disabled="isDistributing"
          >
            取消
          </button>
          <button
            @click="confirmDistribute"
            class="px-4 py-2 bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white rounded-lg transition-all flex items-center space-x-2"
            :disabled="isDistributing"
          >
            <el-icon v-if="isDistributing" class="animate-spin"><Loading /></el-icon>
            <span>{{ isDistributing ? '发放中...' : '确认发放' }}</span>
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox, ElIcon } from 'element-plus'
import { Money, Close, Loading } from '@element-plus/icons-vue'
import { useAdminStore } from '../../stores/admin'

const adminStore = useAdminStore()

// 搜索和筛选
const searchKeyword = ref('')
const winnerFilter = ref('')
const currentPage = ref(1)
const pageSize = ref(20)

// 对话框
const detailDialogVisible = ref(false)
const selectedRecord = ref<any>(null)
const distributeDialogVisible = ref(false)
const distributeRecord = ref<any>(null)
const distributeRemark = ref('')
const isDistributing = ref(false)

// 加载状态
const isLoading = ref(false)



// 过滤后的记录列表
const filteredRecords = computed(() => {
  let records = adminStore.lotteryRecords || []

  // 关键词搜索
  if (searchKeyword.value) {
    const keyword = searchKeyword.value.toLowerCase()
    records = records.filter(record =>
      record.platformId.toLowerCase().includes(keyword) ||
      record.user?.username?.toLowerCase().includes(keyword)
    )
  }

  // 中奖状态筛选
  if (winnerFilter.value !== '') {
    const isWinner = winnerFilter.value === 'true'
    records = records.filter(record => record.isWinner === isWinner)
  }

  return records
})

// 分页后的记录
const paginatedRecords = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value
  const end = start + pageSize.value
  return filteredRecords.value.slice(start, end)
})

// 总页数
const totalPages = computed(() => {
  return Math.ceil(filteredRecords.value.length / pageSize.value)
})

// 格式化金额
const formatAmount = (amount: number) => {
  return amount.toLocaleString('zh-CN', { minimumFractionDigits: 2 })
}

// 格式化日期
const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleString('zh-CN')
}



// 格式化抽奖结果
const formatDrawResult = (result: any) => {
  try {
    return JSON.stringify(JSON.parse(result), null, 2)
  } catch {
    return result
  }
}

// 搜索
const handleSearch = () => {
  currentPage.value = 1
}

// 刷新
const handleRefresh = async () => {
  isLoading.value = true
  try {
    await loadRecords()
    ElMessage.success('抽奖记录已刷新')
  } finally {
    isLoading.value = false
  }
}



// 查看记录详情
const handleViewDetails = (record: any) => {
  selectedRecord.value = record
  detailDialogVisible.value = true
}

// 发放奖品
const handleDistribute = (record: any) => {
  distributeRecord.value = record
  distributeRemark.value = ''
  distributeDialogVisible.value = true
}

// 确认发放
const confirmDistribute = async () => {
  if (!distributeRecord.value) return

  try {
    isDistributing.value = true

    const response = await adminStore.distributePrize(
      distributeRecord.value.id,
      distributeRemark.value
    )

    if (response.success) {
      ElMessage.success('奖品发放成功')
      distributeDialogVisible.value = false

      // 更新本地记录状态
      const record = adminStore.lotteryRecords.find((r: any) => r.id === distributeRecord.value?.id)
      if (record) {
        record.status = '已发放'
        record.distributedAt = new Date().toISOString()
      }

      // 重新加载数据
      await loadRecords()
    } else {
      ElMessage.error(response.message || '发放失败')
    }
  } catch (error) {
    console.error('发放奖品失败:', error)
    ElMessage.error('发放失败，请稍后重试')
  } finally {
    isDistributing.value = false
  }
}

// 加载抽奖记录
const loadRecords = async () => {
  isLoading.value = true
  try {
    const result = await adminStore.fetchLotteryRecords()
    if (!result.success) {
      ElMessage.error(result.message || '加载抽奖记录失败')
    }
  } finally {
    isLoading.value = false
  }
}

onMounted(() => {
  loadRecords()
})
</script>
