const bcrypt = require('bcryptjs')
const { sequelize } = require('../config/database')
const { Admin, User, LotteryPeriod, PrizeConfig } = require('../models')

/**
 * 创建数据库表结构
 */
const createTables = async () => {
  try {
    console.log('🔄 开始创建数据库表结构...')

    // 同步所有模型到数据库，不强制修改现有表结构
    await sequelize.sync({ force: false, alter: false })

    console.log('✅ 数据库表结构创建完成')
    return true
  } catch (error) {
    console.error('❌ 创建数据库表结构失败:', error)
    console.log('⚠️  如果是索引冲突，可以尝试删除数据库重新创建')
    return false
  }
}

/**
 * 初始化管理员账户
 */
const initializeAdminAccount = async () => {
  try {
    console.log('🔄 检查管理员账户...')
    
    // 检查是否已存在管理员
    const existingAdmin = await Admin.findOne({ where: { username: 'admin' } })
    
    if (!existingAdmin) {
      // 创建默认管理员账户
      const hashedPassword = await bcrypt.hash('admin123', 10)
      
      await Admin.create({
        username: 'admin',
        passwordHash: hashedPassword,
        email: '<EMAIL>',
        role: '超级管理员',
        status: '正常'
      })
      
      console.log('✅ 默认管理员账户创建成功')
      console.log('📋 管理员登录信息:')
      console.log('   用户名: admin')
      console.log('   密码: admin123')
    } else {
      console.log('✅ 管理员账户已存在')
    }
    
    return true
  } catch (error) {
    console.error('❌ 初始化管理员账户失败:', error)
    return false
  }
}

/**
 * 初始化测试数据
 */
const initializeTestData = async () => {
  try {
    console.log('🔄 检查测试数据...')
    
    // 检查是否已有抽奖期次
    const existingPeriod = await LotteryPeriod.findOne()
    
    if (!existingPeriod) {
      console.log('🔄 创建测试抽奖期次...')
      
      // 创建测试期次
      const period = await LotteryPeriod.create({
        periodName: '2024年第1期',
        periodNumber: 1,
        startTime: new Date(),
        endTime: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30天后
        drawTime: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000),
        status: '进行中',
        maxAttemptsPerUser: 3,
        totalParticipants: 0,
        totalAttempts: 0
      })
      
      // 创建奖品配置
      const prizes = [
        {
          periodId: period.id,
          prizeLevel: 1,
          prizeName: '特等奖',
          prizeAmount: 10000.00,
          probability: 0.0001,
          colorClass: 'bg-gradient-to-br from-red-500 to-red-600',
          sortOrder: 1
        },
        {
          periodId: period.id,
          prizeLevel: 2,
          prizeName: '一等奖',
          prizeAmount: 1000.00,
          probability: 0.001,
          colorClass: 'bg-gradient-to-br from-purple-500 to-purple-600',
          sortOrder: 2
        },
        {
          periodId: period.id,
          prizeLevel: 3,
          prizeName: '二等奖',
          prizeAmount: 100.00,
          probability: 0.01,
          colorClass: 'bg-gradient-to-br from-blue-500 to-blue-600',
          sortOrder: 3
        },
        {
          periodId: period.id,
          prizeLevel: 4,
          prizeName: '三等奖',
          prizeAmount: 10.00,
          probability: 0.1,
          colorClass: 'bg-gradient-to-br from-green-500 to-green-600',
          sortOrder: 4
        },
        {
          periodId: period.id,
          prizeLevel: 5,
          prizeName: '参与奖',
          prizeAmount: 1.00,
          probability: 0.3,
          colorClass: 'bg-gradient-to-br from-yellow-500 to-yellow-600',
          sortOrder: 5
        },
        {
          periodId: period.id,
          prizeLevel: 6,
          prizeName: '谢谢参与',
          prizeAmount: 0.00,
          probability: 0.5889,
          colorClass: 'bg-gradient-to-br from-gray-500 to-gray-600',
          sortOrder: 6
        }
      ]
      
      await PrizeConfig.bulkCreate(prizes)
      
      console.log('✅ 测试抽奖期次和奖品配置创建完成')
    } else {
      console.log('✅ 测试数据已存在')
    }
    
    return true
  } catch (error) {
    console.error('❌ 初始化测试数据失败:', error)
    return false
  }
}

/**
 * 创建测试用户
 */
const createTestUsers = async () => {
  try {
    console.log('🔄 检查测试用户...')
    
    const existingUser = await User.findOne({ where: { username: 'testuser' } })
    
    if (!existingUser) {
      const hashedPassword = await bcrypt.hash('123456', 10)
      
      await User.create({
        platformId: 'TEST001',
        username: 'testuser',
        passwordHash: hashedPassword,
        status: '正常'
      })
      
      console.log('✅ 测试用户创建成功')
      console.log('📋 测试用户登录信息:')
      console.log('   用户名: testuser')
      console.log('   密码: 123456')
      console.log('   平台ID: TEST001')
    } else {
      console.log('✅ 测试用户已存在')
    }
    
    return true
  } catch (error) {
    console.error('❌ 创建测试用户失败:', error)
    return false
  }
}

/**
 * 完整的数据库初始化
 */
const initializeDatabase = async () => {
  try {
    console.log('🚀 开始初始化数据库...')
    
    // 1. 创建表结构
    const tablesCreated = await createTables()
    if (!tablesCreated) {
      throw new Error('创建数据库表结构失败')
    }
    
    // 2. 初始化管理员账户
    const adminInitialized = await initializeAdminAccount()
    if (!adminInitialized) {
      throw new Error('初始化管理员账户失败')
    }
    
    // 3. 初始化测试数据
    const testDataInitialized = await initializeTestData()
    if (!testDataInitialized) {
      throw new Error('初始化测试数据失败')
    }
    
    // 4. 创建测试用户
    const testUsersCreated = await createTestUsers()
    if (!testUsersCreated) {
      throw new Error('创建测试用户失败')
    }
    
    console.log('🎉 数据库初始化完成!')
    return true
  } catch (error) {
    console.error('❌ 数据库初始化失败:', error)
    return false
  }
}

module.exports = {
  createTables,
  initializeAdminAccount,
  initializeTestData,
  createTestUsers,
  initializeDatabase
}
