<template>
  <div class="admin-managers min-h-screen bg-gradient-to-br from-gray-900 via-black to-gray-800 p-6">
    <!-- 页面头部 -->
    <div class="mb-8">
      <div class="flex items-center justify-between">
        <div>
          <h2 class="text-3xl font-bold text-white mb-2 flex items-center">
            <el-icon class="mr-3 text-gold-400" size="32"><UserFilled /></el-icon>
            管理员管理
          </h2>
          <p class="text-gray-400">管理平台管理员账户和权限</p>
        </div>
        <div class="flex items-center space-x-4">
          <div class="px-4 py-2 bg-gradient-to-r from-gold-500/20 to-gold-600/20 border border-gold-500/30 rounded-lg">
            <span class="text-gold-400 text-sm font-medium">总管理员数: {{ filteredAdmins.length }}</span>
          </div>
          <button
            @click="showAddDialog = true"
            class="px-4 py-2 bg-green-500/20 hover:bg-green-500/30 border border-green-500/30 text-green-400 hover:text-green-300 rounded-lg transition-all duration-200 flex items-center space-x-2"
          >
            <el-icon><Plus /></el-icon>
            <span>添加管理员</span>
          </button>
          <button
            @click="handleRefresh"
            class="px-4 py-2 bg-blue-500/20 hover:bg-blue-500/30 border border-blue-500/30 text-blue-400 hover:text-blue-300 rounded-lg transition-all duration-200 flex items-center space-x-2"
          >
            <el-icon><Refresh /></el-icon>
            <span>刷新数据</span>
          </button>
        </div>
      </div>
    </div>

    <!-- 搜索和筛选 -->
    <div class="bg-gradient-to-br from-gray-800 to-gray-900 border border-gold-500/30 rounded-xl shadow-xl p-6 mb-6">
      <div class="flex flex-wrap gap-4 items-center">
        <div class="flex-1 min-w-[300px]">
          <input
            v-model="searchKeyword"
            placeholder="搜索管理员用户名..."
            class="w-full px-4 py-3 bg-gray-700 border border-gray-600 text-white placeholder-gray-400 rounded-lg focus:outline-none focus:border-gold-500 focus:ring-1 focus:ring-gold-500 transition-all"
          />
        </div>

        <select
          v-model="statusFilter"
          class="px-4 py-3 bg-gray-700 border border-gray-600 text-white rounded-lg focus:outline-none focus:border-gold-500 min-w-[150px]"
        >
          <option value="">全部状态</option>
          <option value="正常">正常</option>
          <option value="禁用">禁用</option>
        </select>



        <button
          @click="handleSearch"
          class="px-6 py-3 bg-gradient-to-r from-gold-500 to-gold-600 hover:from-gold-600 hover:to-gold-700 text-black font-medium rounded-lg transition-all duration-200 flex items-center space-x-2"
        >
          <el-icon><Search /></el-icon>
          <span>搜索</span>
        </button>
      </div>
    </div>

    <!-- 管理员列表 -->
    <div class="bg-gradient-to-br from-gray-800 to-gray-900 border border-gold-500/30 rounded-xl shadow-xl overflow-hidden">
      <!-- 表格头部 -->
      <div class="px-6 py-4 border-b border-gray-700">
        <h3 class="text-lg font-semibold text-white flex items-center">
          <el-icon class="mr-2 text-gold-400"><List /></el-icon>
          管理员列表
        </h3>
      </div>

      <!-- 加载状态 -->
      <div v-if="isLoading" class="flex items-center justify-center py-12">
        <div class="text-center">
          <div class="w-8 h-8 border-2 border-gold-500 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p class="text-gray-400">加载中...</p>
        </div>
      </div>

      <!-- 管理员卡片列表 -->
      <div v-else-if="paginatedAdmins.length > 0" class="p-6">
        <div class="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
          <div
            v-for="admin in paginatedAdmins"
            :key="admin.id"
            class="bg-gradient-to-br from-gray-700 to-gray-800 border border-gray-600 rounded-lg p-6 hover:border-gold-500/50 transition-all duration-300 group"
          >
            <!-- 管理员头部信息 -->
            <div class="flex items-center justify-between mb-4">
              <div class="flex items-center space-x-3">
                <div class="w-12 h-12 bg-gradient-to-br from-gold-400 to-gold-600 rounded-full flex items-center justify-center">
                  <span class="text-lg font-bold text-black">{{ admin.username.charAt(0).toUpperCase() }}</span>
                </div>
                <div>
                  <h4 class="text-white font-semibold">{{ admin.username }}</h4>
                  <p class="text-gray-400 text-sm">ID: {{ admin.id }}</p>
                </div>
              </div>
              <div :class="admin.status === '正常' ? 'bg-green-500/20 border-green-500/30 text-green-400' : 'bg-red-500/20 border-red-500/30 text-red-400'" class="px-3 py-1 border rounded-full">
                <span class="text-xs font-medium">{{ admin.status }}</span>
              </div>
            </div>

            <!-- 管理员详细信息 -->
            <div class="space-y-3 mb-4">
              <div class="flex justify-between items-center">
                <span class="text-gray-400 text-sm">角色</span>
                <span class="px-2 py-1 rounded text-xs font-medium bg-blue-500/20 border-blue-500/30 text-blue-400">
                  管理员
                </span>
              </div>
              <div class="flex justify-between items-center">
                <span class="text-gray-400 text-sm">最后登录</span>
                <span class="text-gray-300 text-sm">{{ formatDate(admin.lastLoginAt) }}</span>
              </div>
              <div class="flex justify-between items-center">
                <span class="text-gray-400 text-sm">创建时间</span>
                <span class="text-gray-300 text-sm">{{ formatDate(admin.createdAt) }}</span>
              </div>
            </div>

            <!-- 操作按钮 -->
            <div class="flex space-x-2">
              <button
                v-if="admin.status === '正常'"
                @click="handleUpdateStatus(admin.id, '禁用')"
                class="flex-1 px-3 py-2 bg-red-500/20 hover:bg-red-500/30 border border-red-500/30 text-red-400 hover:text-red-300 rounded-lg transition-all text-sm"
              >
                禁用
              </button>
              <button
                v-else
                @click="handleUpdateStatus(admin.id, '正常')"
                class="flex-1 px-3 py-2 bg-green-500/20 hover:bg-green-500/30 border border-green-500/30 text-green-400 hover:text-green-300 rounded-lg transition-all text-sm"
              >
                启用
              </button>

              <button
                @click="handleEdit(admin)"
                class="flex-1 px-3 py-2 bg-blue-500/20 hover:bg-blue-500/30 border border-blue-500/30 text-blue-400 hover:text-blue-300 rounded-lg transition-all text-sm"
              >
                编辑
              </button>

              <button
                @click="handleDelete(admin.id)"
                class="px-3 py-2 bg-red-500/20 hover:bg-red-500/30 border border-red-500/30 text-red-400 hover:text-red-300 rounded-lg transition-all text-sm"
              >
                删除
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- 空状态 -->
      <div v-else class="flex items-center justify-center py-12">
        <div class="text-center">
          <el-icon size="64" class="text-gray-600 mb-4"><UserFilled /></el-icon>
          <p class="text-gray-400 text-lg mb-2">暂无管理员数据</p>
          <p class="text-gray-500 text-sm">请检查搜索条件或稍后重试</p>
        </div>
      </div>

      <!-- 分页 -->
      <div v-if="filteredAdmins.length > 0" class="px-6 py-4 border-t border-gray-700 flex justify-between items-center">
        <div class="text-gray-400 text-sm">
          共 {{ filteredAdmins.length }} 条记录，每页显示 {{ pageSize }} 条
        </div>
        <div class="flex items-center space-x-2">
          <button
            @click="currentPage = Math.max(1, currentPage - 1)"
            :disabled="currentPage === 1"
            class="px-3 py-1 bg-gray-700 hover:bg-gray-600 disabled:bg-gray-800 disabled:text-gray-600 text-gray-300 rounded transition-all"
          >
            上一页
          </button>
          <span class="px-3 py-1 bg-gold-500/20 border border-gold-500/30 text-gold-400 rounded">
            {{ currentPage }} / {{ totalPages }}
          </span>
          <button
            @click="currentPage = Math.min(totalPages, currentPage + 1)"
            :disabled="currentPage === totalPages"
            class="px-3 py-1 bg-gray-700 hover:bg-gray-600 disabled:bg-gray-800 disabled:text-gray-600 text-gray-300 rounded transition-all"
          >
            下一页
          </button>
        </div>
      </div>
    </div>

    <!-- 添加管理员对话框 -->
    <div v-if="showAddDialog" class="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
      <div class="bg-gradient-to-br from-gray-800 to-gray-900 border border-gold-500/30 rounded-xl shadow-2xl w-full max-w-md">
        <!-- 对话框头部 -->
        <div class="flex items-center justify-between p-6 border-b border-gray-700">
          <h3 class="text-xl font-semibold text-white flex items-center">
            <el-icon class="mr-3 text-gold-400" size="24"><Plus /></el-icon>
            添加管理员
          </h3>
          <button
            @click="showAddDialog = false"
            class="p-2 text-gray-400 hover:text-white hover:bg-gray-700 rounded-lg transition-all"
          >
            <el-icon size="20"><Close /></el-icon>
          </button>
        </div>

        <!-- 表单内容 -->
        <div class="p-6">
          <form @submit.prevent="handleAddAdmin" class="space-y-4">
            <div>
              <label class="block text-sm font-medium text-gray-400 mb-2">用户名</label>
              <input
                v-model="addForm.username"
                type="text"
                required
                class="w-full px-4 py-3 bg-gray-700 border border-gray-600 text-white placeholder-gray-400 rounded-lg focus:outline-none focus:border-gold-500 focus:ring-1 focus:ring-gold-500 transition-all"
                placeholder="请输入用户名"
              />
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-400 mb-2">密码</label>
              <input
                v-model="addForm.password"
                type="password"
                required
                class="w-full px-4 py-3 bg-gray-700 border border-gray-600 text-white placeholder-gray-400 rounded-lg focus:outline-none focus:border-gold-500 focus:ring-1 focus:ring-gold-500 transition-all"
                placeholder="请输入密码"
              />
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-400 mb-2">角色</label>
              <div class="w-full px-4 py-3 bg-gray-700 border border-gray-600 text-white rounded-lg">
                管理员
              </div>
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-400 mb-2">状态</label>
              <select
                v-model="addForm.status"
                required
                class="w-full px-4 py-3 bg-gray-700 border border-gray-600 text-white rounded-lg focus:outline-none focus:border-gold-500"
              >
                <option value="正常">正常</option>
                <option value="禁用">禁用</option>
              </select>
            </div>

            <!-- 操作按钮 -->
            <div class="flex justify-end space-x-3 pt-4">
              <button
                type="button"
                @click="showAddDialog = false"
                class="px-6 py-2 bg-gray-700 hover:bg-gray-600 text-gray-300 hover:text-white rounded-lg transition-all"
              >
                取消
              </button>
              <button
                type="submit"
                class="px-6 py-2 bg-gradient-to-r from-gold-500 to-gold-600 hover:from-gold-600 hover:to-gold-700 text-black font-medium rounded-lg transition-all"
              >
                添加
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>

    <!-- 编辑管理员对话框 -->
    <div v-if="showEditDialog" class="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
      <div class="bg-gradient-to-br from-gray-800 to-gray-900 border border-gold-500/30 rounded-xl shadow-2xl w-full max-w-md">
        <!-- 对话框头部 -->
        <div class="flex items-center justify-between p-6 border-b border-gray-700">
          <h3 class="text-xl font-semibold text-white flex items-center">
            <el-icon class="mr-3 text-gold-400" size="24"><Edit /></el-icon>
            编辑管理员
          </h3>
          <button
            @click="showEditDialog = false"
            class="p-2 text-gray-400 hover:text-white hover:bg-gray-700 rounded-lg transition-all"
          >
            <el-icon size="20"><Close /></el-icon>
          </button>
        </div>

        <!-- 表单内容 -->
        <div class="p-6">
          <form @submit.prevent="handleUpdateAdmin" class="space-y-4">
            <div>
              <label class="block text-sm font-medium text-gray-400 mb-2">用户名</label>
              <input
                v-model="editForm.username"
                type="text"
                required
                class="w-full px-4 py-3 bg-gray-700 border border-gray-600 text-white placeholder-gray-400 rounded-lg focus:outline-none focus:border-gold-500 focus:ring-1 focus:ring-gold-500 transition-all"
                placeholder="请输入用户名"
              />
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-400 mb-2">新密码</label>
              <input
                v-model="editForm.password"
                type="password"
                class="w-full px-4 py-3 bg-gray-700 border border-gray-600 text-white placeholder-gray-400 rounded-lg focus:outline-none focus:border-gold-500 focus:ring-1 focus:ring-gold-500 transition-all"
                placeholder="留空则不修改密码"
              />
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-400 mb-2">角色</label>
              <div class="w-full px-4 py-3 bg-gray-700 border border-gray-600 text-white rounded-lg">
                管理员
              </div>
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-400 mb-2">状态</label>
              <select
                v-model="editForm.status"
                required
                class="w-full px-4 py-3 bg-gray-700 border border-gray-600 text-white rounded-lg focus:outline-none focus:border-gold-500"
              >
                <option value="正常">正常</option>
                <option value="禁用">禁用</option>
              </select>
            </div>

            <!-- 操作按钮 -->
            <div class="flex justify-end space-x-3 pt-4">
              <button
                type="button"
                @click="showEditDialog = false"
                class="px-6 py-2 bg-gray-700 hover:bg-gray-600 text-gray-300 hover:text-white rounded-lg transition-all"
              >
                取消
              </button>
              <button
                type="submit"
                class="px-6 py-2 bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white font-medium rounded-lg transition-all"
              >
                保存
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useAdminStore } from '../../stores/admin'

const adminStore = useAdminStore()

// 响应式数据
const searchKeyword = ref('')
const statusFilter = ref('')
const currentPage = ref(1)
const pageSize = ref(12)
const isLoading = ref(false)
const showAddDialog = ref(false)
const showEditDialog = ref(false)
const selectedAdmin = ref(null)

// 表单数据
const addForm = ref({
  username: '',
  password: '',
  email: '',
  role: '管理员',
  status: '正常'
})

const editForm = ref({
  id: null as number | null,
  username: '',
  email: '',
  role: '管理员',
  status: '正常',
  password: ''
})

// 过滤后的管理员列表
const filteredAdmins = computed(() => {
  let filtered = adminStore.admins

  // 关键词搜索
  if (searchKeyword.value) {
    const keyword = searchKeyword.value.toLowerCase()
    filtered = filtered.filter(admin =>
      admin.username.toLowerCase().includes(keyword)
    )
  }

  // 状态筛选
  if (statusFilter.value) {
    filtered = filtered.filter(admin => admin.status === statusFilter.value)
  }

  return filtered
})

// 分页相关计算属性
const totalPages = computed(() => Math.ceil(filteredAdmins.value.length / pageSize.value))

const paginatedAdmins = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value
  const end = start + pageSize.value
  return filteredAdmins.value.slice(start, end)
})

// 工具方法

const formatDate = (dateString: string | undefined) => {
  if (!dateString) return '从未登录'
  return new Date(dateString).toLocaleString('zh-CN')
}

// 事件处理方法
const handleSearch = () => {
  currentPage.value = 1
}



const handleUpdateStatus = async (adminId: number, status: string) => {
  try {
    await ElMessageBox.confirm(
      `确定要${status === '禁用' ? '禁用' : '启用'}该管理员吗？`,
      '确认操作',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )

    // 调用API更新状态
    await adminStore.updateAdminStatus(adminId, status)
    ElMessage.success(`管理员已${status === '禁用' ? '禁用' : '启用'}`)
  } catch (error: any) {
    if (error.message) {
      ElMessage.error(error.message)
    }
    // 用户取消或其他错误
  }
}

const handleEdit = (admin: any) => {
  selectedAdmin.value = admin
  // 填充编辑表单
  editForm.value = {
    id: admin.id,
    username: admin.username,
    email: admin.email || '',
    role: admin.role || '管理员',
    status: admin.status,
    password: ''
  }
  showEditDialog.value = true
}

const handleAddAdmin = async () => {
  try {
    // 验证表单
    if (!addForm.value.username || !addForm.value.password) {
      ElMessage.error('请填写所有必填字段')
      return
    }

    // 调用API创建管理员
    await adminStore.createAdmin({
      username: addForm.value.username,
      password: addForm.value.password,
      email: addForm.value.email,
      role: addForm.value.role
    })

    // 重置表单
    addForm.value = {
      username: '',
      password: '',
      email: '',
      role: '管理员',
      status: '正常'
    }

    showAddDialog.value = false
    ElMessage.success('管理员添加成功')
  } catch (error: any) {
    ElMessage.error(error.message || '添加管理员失败')
  }
}

const handleUpdateAdmin = async () => {
  try {
    // 验证表单
    if (!editForm.value.username) {
      ElMessage.error('请填写用户名')
      return
    }

    // 调用API更新管理员完整信息
    if (editForm.value.id) {
      const updateData: any = {
        username: editForm.value.username,
        email: editForm.value.email,
        role: editForm.value.role,
        status: editForm.value.status
      }

      // 只有在输入了新密码时才包含密码字段
      if (editForm.value.password && editForm.value.password.trim() !== '') {
        updateData.password = editForm.value.password
      }

      await adminStore.updateAdminInfo(editForm.value.id, updateData)
    }

    showEditDialog.value = false
    ElMessage.success('管理员信息更新成功')

    // 清空编辑表单
    editForm.value = {
      id: null,
      username: '',
      email: '',
      role: '管理员',
      status: '正常',
      password: ''
    }
  } catch (error: any) {
    ElMessage.error(error.message || '更新管理员信息失败')
  }
}

const handleDelete = async (adminId: number) => {
  try {
    await ElMessageBox.confirm(
      '确定要删除该管理员吗？此操作不可恢复！',
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'error',
      }
    )

    // 调用API删除管理员
    await adminStore.deleteAdmin(adminId)
    ElMessage.success('管理员已删除')
  } catch (error: any) {
    if (error.message) {
      ElMessage.error(error.message)
    }
    // 用户取消或其他错误
  }
}

const handleRefresh = async () => {
  try {
    isLoading.value = true
    await adminStore.fetchAdmins()
    ElMessage.success('数据刷新成功')
  } catch (error: any) {
    ElMessage.error(error.message || '刷新数据失败')
  } finally {
    isLoading.value = false
  }
}

onMounted(async () => {
  // 初始化数据
  await handleRefresh()
})
</script>

<style scoped>
.admin-managers {
  font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
}

/* 深色主题金色装饰 */
.text-gold-400 {
  color: #d4af37;
}

.bg-gold-500 {
  background-color: #d4af37;
}

.border-gold-500 {
  border-color: #d4af37;
}

/* 卡片悬停效果 */
.group:hover {
  transform: translateY(-2px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

/* 渐变背景动画 */
@keyframes gradient {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

.bg-gradient-to-br {
  background-size: 200% 200%;
  animation: gradient 8s ease infinite;
}

/* 加载动画 */
@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.animate-spin {
  animation: spin 1s linear infinite;
}

/* 自定义滚动条 */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.1);
}

::-webkit-scrollbar-thumb {
  background: rgba(212, 175, 55, 0.3);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(212, 175, 55, 0.5);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .admin-managers {
    padding: 1rem;
  }

  .grid {
    grid-template-columns: 1fr;
  }

  .flex-wrap {
    flex-direction: column;
    align-items: stretch;
  }

  .min-w-300 {
    min-width: auto;
  }

  .min-w-150 {
    min-width: auto;
  }
}

@media (max-width: 640px) {
  .text-3xl {
    font-size: 1.5rem;
  }

  .space-x-4 > * + * {
    margin-left: 0.5rem;
  }

  .px-6 {
    padding-left: 1rem;
    padding-right: 1rem;
  }
}
</style>