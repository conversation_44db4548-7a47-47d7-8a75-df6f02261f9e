const { DataTypes } = require('sequelize')
const { sequelize } = require('../config/database')

// 用户模型
const User = sequelize.define('User', {
  id: {
    type: DataTypes.BIGINT,
    primaryKey: true,
    autoIncrement: true
  },
  platformId: {
    type: DataTypes.STRING(50),
    allowNull: false,
    unique: true,
    field: 'platform_id'
  },
  username: {
    type: DataTypes.STRING(100),
    allowNull: false,
    unique: true
  },
  passwordHash: {
    type: DataTypes.STRING(255),
    allowNull: false,
    field: 'password_hash'
  },
  status: {
    type: DataTypes.ENUM('正常', '禁用'),
    defaultValue: '正常'
  },
  totalWinnings: {
    type: DataTypes.DECIMAL(12, 2),
    defaultValue: 0,
    field: 'total_winnings'
  },
  customLotteryEnabled: {
    type: DataTypes.BOOLEAN,
    defaultValue: false,
    field: 'custom_lottery_enabled',
    comment: '是否启用个人抽奖率设置(0:使用全局设置, 1:使用个人设置)'
  }
}, {
  tableName: 'users',
  underscored: true
})

module.exports = User
