<template>
  <div class="admin-layout min-h-screen bg-gradient-to-br from-gray-900 via-black to-gray-800">
    <!-- 顶部导航栏 -->
    <header class="bg-gradient-to-r from-gray-800 to-gray-900 shadow-xl border-b border-gold-500/30">
      <div class="px-6 py-4 flex justify-between items-center">
        <div class="flex items-center space-x-4">
          <div class="flex items-center space-x-3">
            <div class="w-10 h-10 bg-gradient-to-br from-gold-400 to-gold-600 rounded-lg flex items-center justify-center">
              <span class="text-lg font-bold text-black">🎰</span>
            </div>
            <div>
              <h1 class="text-xl font-bold text-white">澳门金沙管理后台</h1>
              <p class="text-xs text-gray-400">Macau Sands Admin Panel</p>
            </div>
          </div>
          <div class="px-3 py-1 bg-gradient-to-r from-gold-500 to-gold-600 rounded-full">
            <span class="text-xs font-bold text-black">ADMIN</span>
          </div>
        </div>
        <div class="flex items-center space-x-4">
          <!-- 通知图标 -->
          <div class="relative">
            <button class="p-2 text-gray-400 hover:text-gold-400 transition-colors">
              <el-icon size="20"><Bell /></el-icon>
            </button>
            <div class="absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full animate-pulse"></div>
          </div>

          <!-- 用户信息 -->
          <div class="flex items-center space-x-3">
            <div class="text-right">
              <div class="text-sm font-medium text-white">管理员</div>
              <div class="text-xs text-gray-400">{{ currentTime }}</div>
            </div>
            <div class="w-8 h-8 bg-gradient-to-br from-gold-400 to-gold-600 rounded-full flex items-center justify-center">
              <span class="text-sm font-bold text-black">A</span>
            </div>
          </div>

          <!-- 退出按钮 -->
          <button
            @click="handleLogout"
            class="px-4 py-2 bg-red-600/20 hover:bg-red-600/30 border border-red-500/30 text-red-400 hover:text-red-300 rounded-lg transition-all duration-200 flex items-center space-x-2"
          >
            <el-icon><SwitchButton /></el-icon>
            <span class="text-sm">退出</span>
          </button>
        </div>
      </div>
    </header>

    <div class="flex">
      <!-- 侧边栏 -->
      <aside class="w-64 bg-gradient-to-b from-gray-800 to-gray-900 shadow-xl border-r border-gold-500/20 min-h-screen">
        <!-- 侧边栏头部 -->
        <div class="p-4 border-b border-gray-700">
          <div class="text-center">
            <div class="text-sm text-gray-400 mb-1">当前状态</div>
            <div class="flex items-center justify-center space-x-2">
              <div class="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
              <span class="text-green-400 text-sm font-medium">系统正常</span>
            </div>
          </div>
        </div>

        <!-- 导航菜单 -->
        <nav class="p-4">
          <div class="space-y-2">
            <router-link
              v-for="item in menuItems"
              :key="item.path"
              :to="item.path"
              :class="[
                'flex items-center space-x-3 px-4 py-3 rounded-lg transition-all duration-200 group',
                activeMenu === item.path
                  ? 'bg-gradient-to-r from-gold-500/20 to-gold-600/20 border border-gold-500/30 text-gold-400'
                  : 'text-gray-400 hover:text-white hover:bg-gray-700/50'
              ]"
            >
              <el-icon :size="18" :class="activeMenu === item.path ? 'text-gold-400' : 'text-gray-500 group-hover:text-gray-300'">
                <component :is="item.icon" />
              </el-icon>
              <span class="font-medium">{{ item.title }}</span>
              <div v-if="activeMenu === item.path" class="ml-auto w-2 h-2 bg-gold-500 rounded-full"></div>
            </router-link>
          </div>
        </nav>


      </aside>

      <!-- 主内容区域 -->
      <main class="flex-1 p-6 overflow-auto">
        <router-view />
      </main>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, onMounted, onUnmounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useAuthStore } from '../../stores/auth'

const route = useRoute()
const router = useRouter()
const authStore = useAuthStore()

// 响应式数据
const currentTime = ref('')

// 菜单项配置
const menuItems = [
  {
    path: '/admin/dashboard',
    title: '数据统计',
    icon: 'DataAnalysis'
  },
  {
    path: '/admin/users',
    title: '用户管理',
    icon: 'User'
  },
  {
    path: '/admin/admins',
    title: '管理员管理',
    icon: 'UserFilled'
  },
  {
    path: '/admin/lottery-records',
    title: '抽奖记录',
    icon: 'List'
  },
  {
    path: '/admin/lottery-periods',
    title: '期次管理',
    icon: 'Calendar'
  },
  {
    path: '/admin/user-logs',
    title: '用户操作日志',
    icon: 'Document'
  },
  {
    path: '/admin/admin-logs',
    title: '管理员操作日志',
    icon: 'Notebook'
  },
  {
    path: '/admin/settings',
    title: '系统设置',
    icon: 'Setting'
  }
]

// 当前激活的菜单项
const activeMenu = computed(() => route.path)

// 更新时间
const updateTime = () => {
  const now = new Date()
  currentTime.value = now.toLocaleTimeString('zh-CN', {
    hour12: false,
    hour: '2-digit',
    minute: '2-digit'
  })
}

// 定时器
let timeInterval: number

// 退出登录
const handleLogout = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要退出登录吗？',
      '退出确认',
      {
        confirmButtonText: '确定退出',
        cancelButtonText: '取消',
        type: 'warning',
        customClass: 'dark-message-box'
      }
    )

    await authStore.adminLogout()
    ElMessage.success('已安全退出登录')
    router.push('/admin/login')
  } catch {
    // 用户取消
  }
}

onMounted(() => {
  updateTime()
  timeInterval = setInterval(updateTime, 1000)
})

onUnmounted(() => {
  if (timeInterval) {
    clearInterval(timeInterval)
  }
})
</script>

<style scoped>
.admin-layout {
  font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
}

/* 自定义滚动条 */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.1);
}

::-webkit-scrollbar-thumb {
  background: rgba(212, 175, 55, 0.3);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(212, 175, 55, 0.5);
}

/* 路由链接样式 */
.router-link-active {
  background: linear-gradient(135deg, rgba(212, 175, 55, 0.2), rgba(212, 175, 55, 0.3)) !important;
  border: 1px solid rgba(212, 175, 55, 0.3) !important;
  color: #d4af37 !important;
}

/* 动画效果 */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.admin-layout {
  animation: fadeIn 0.5s ease-out;
}

/* 脉冲动画 */
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* 悬停效果 */
.group:hover {
  transform: translateX(4px);
  transition: all 0.2s ease;
}

/* Element Plus 深色主题覆盖 */
:deep(.el-message-box) {
  background: linear-gradient(135deg, #1f2937, #111827);
  border: 1px solid rgba(212, 175, 55, 0.3);
}

:deep(.el-message-box__title) {
  color: #ffffff;
}

:deep(.el-message-box__content) {
  color: #d1d5db;
}

:deep(.el-button--primary) {
  background: linear-gradient(135deg, #d4af37, #b8941f);
  border-color: #d4af37;
  color: #000000;
}

:deep(.el-button--primary:hover) {
  background: linear-gradient(135deg, #b8941f, #9a7b1a);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .admin-layout aside {
    width: 200px;
  }

  .admin-layout main {
    padding: 1rem;
  }
}
</style>
