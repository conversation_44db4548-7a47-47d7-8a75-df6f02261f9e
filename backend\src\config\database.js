const { Sequelize } = require('sequelize')
require('dotenv').config()

// MySQL数据库连接配置
const sequelize = new Sequelize({
  dialect: 'mysql',
  host: process.env.DB_HOST || 'localhost',
  port: process.env.DB_PORT || 3306,
  username: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || '',
  database: process.env.DB_NAME || 'lottery_system',
  timezone: '+08:00',
  logging: false, // 设置为false减少日志输出
  dialectOptions: {
    connectTimeout: 60000, // 60秒连接超时
  },
  pool: {
    max: 5,
    min: 0,
    acquire: 60000, // 增加到60秒
    idle: 10000
  },
  retry: {
    match: [
      /ETIMEDOUT/,
      /EHOSTUNREACH/,
      /ECONNRESET/,
      /ECONNREFUSED/,
      /TIMEOUT/,
    ],
    max: 3
  }
})

// 测试数据库连接
const testConnection = async () => {
  try {
    await sequelize.authenticate()
    console.log('✅ MySQL数据库连接成功')
    return true
  } catch (error) {
    console.error('❌ 数据库连接失败:', error)
    return false
  }
}

module.exports = {
  sequelize,
  testConnection
}
