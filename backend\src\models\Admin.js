const { DataTypes } = require('sequelize')
const { sequelize } = require('../config/database')

// 管理员模型
const Admin = sequelize.define('Admin', {
  id: {
    type: DataTypes.BIGINT,
    primaryKey: true,
    autoIncrement: true
  },
  username: {
    type: DataTypes.STRING(100),
    allowNull: false,
    unique: true
  },
  passwordHash: {
    type: DataTypes.STRING(255),
    allowNull: false,
    field: 'password_hash'
  },
  email: {
    type: DataTypes.STRING(255),
    allowNull: true
  },
  role: {
    type: DataTypes.ENUM('超级管理员', '管理员', '操作员'),
    defaultValue: '管理员'
  },
  status: {
    type: DataTypes.ENUM('正常', '禁用'),
    defaultValue: '正常'
  },
  lastLoginAt: {
    type: DataTypes.DATE,
    field: 'last_login_at'
  },
  lastLoginIp: {
    type: DataTypes.STRING(45),
    field: 'last_login_ip'
  }
}, {
  tableName: 'admins',
  underscored: true
})

module.exports = Admin
