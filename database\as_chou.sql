/*
Navicat MySQL Data Transfer

Source Server         : 服务器as_chou
Source Server Version : 80036
Source Host           : *************:3306
Source Database       : as_chou

Target Server Type    : MYSQL
Target Server Version : 80036
File Encoding         : 65001

Date: 2025-07-29 01:02:41
*/

SET FOREIGN_KEY_CHECKS=0;

-- ----------------------------
-- Table structure for admins
-- ----------------------------
DROP TABLE IF EXISTS `admins`;
CREATE TABLE `admins` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `username` varchar(100) COLLATE utf8mb4_general_ci NOT NULL,
  `password_hash` varchar(255) COLLATE utf8mb4_general_ci NOT NULL,
  `email` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `role` enum('超级管理员','管理员','操作员') COLLATE utf8mb4_general_ci DEFAULT '管理员',
  `status` enum('正常','禁用') COLLATE utf8mb4_general_ci DEFAULT '正常',
  `last_login_at` datetime DEFAULT NULL,
  `last_login_ip` varchar(45) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `created_at` datetime NOT NULL,
  `updated_at` datetime NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `username` (`username`),
  UNIQUE KEY `username_2` (`username`),
  UNIQUE KEY `username_3` (`username`),
  UNIQUE KEY `username_4` (`username`),
  UNIQUE KEY `username_5` (`username`),
  UNIQUE KEY `username_6` (`username`),
  UNIQUE KEY `username_7` (`username`),
  UNIQUE KEY `username_8` (`username`),
  UNIQUE KEY `username_9` (`username`),
  UNIQUE KEY `username_10` (`username`),
  UNIQUE KEY `username_11` (`username`),
  UNIQUE KEY `username_12` (`username`),
  UNIQUE KEY `username_13` (`username`),
  UNIQUE KEY `username_14` (`username`),
  UNIQUE KEY `username_15` (`username`),
  UNIQUE KEY `username_16` (`username`),
  UNIQUE KEY `username_17` (`username`),
  UNIQUE KEY `username_18` (`username`),
  UNIQUE KEY `username_19` (`username`),
  UNIQUE KEY `username_20` (`username`),
  UNIQUE KEY `username_21` (`username`),
  UNIQUE KEY `username_22` (`username`),
  UNIQUE KEY `username_23` (`username`),
  UNIQUE KEY `username_24` (`username`),
  UNIQUE KEY `username_25` (`username`),
  UNIQUE KEY `username_26` (`username`),
  UNIQUE KEY `username_27` (`username`),
  UNIQUE KEY `username_28` (`username`),
  UNIQUE KEY `username_29` (`username`),
  UNIQUE KEY `username_30` (`username`),
  UNIQUE KEY `username_31` (`username`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- ----------------------------
-- Records of admins
-- ----------------------------
INSERT INTO `admins` VALUES ('1', 'admin', '$2b$10$Y/Tb9H4pQ.l1JYMYkVjBo.3CsYJ.c9YYILBAjtin8xn6Gg6u9DjK2', '<EMAIL>', '超级管理员', '正常', '2025-07-28 15:25:43', '::1', '2025-07-28 04:27:13', '2025-07-28 15:25:43');
INSERT INTO `admins` VALUES ('2', 'aa123456', '$2b$10$pGeMzFlqrHJSepzeWsL1E.mBmnY2TJrTEaXand5h4M7b.WhDqYomm', '', '管理员', '禁用', '2025-07-28 15:11:03', '::1', '2025-07-28 14:46:46', '2025-07-28 15:12:41');

-- ----------------------------
-- Table structure for admin_logs
-- ----------------------------
DROP TABLE IF EXISTS `admin_logs`;
CREATE TABLE `admin_logs` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `admin_id` bigint NOT NULL,
  `action` varchar(50) COLLATE utf8mb4_general_ci NOT NULL COMMENT '操作类型',
  `description` text COLLATE utf8mb4_general_ci NOT NULL COMMENT '操作描述',
  `risk_level` enum('低','中','高') COLLATE utf8mb4_general_ci NOT NULL DEFAULT '低' COMMENT '风险等级',
  `ip_address` varchar(45) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT 'IP地址',
  `user_agent` text COLLATE utf8mb4_general_ci COMMENT '用户代理',
  `details` json DEFAULT NULL COMMENT '详细信息(JSON格式)',
  `created_at` datetime NOT NULL,
  `updated_at` datetime NOT NULL,
  PRIMARY KEY (`id`),
  KEY `admin_logs_admin_id` (`admin_id`),
  KEY `admin_logs_action` (`action`),
  KEY `admin_logs_risk_level` (`risk_level`),
  KEY `admin_logs_created_at` (`created_at`),
  KEY `admin_logs_admin_id_action` (`admin_id`,`action`)
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- ----------------------------
-- Records of admin_logs
-- ----------------------------
INSERT INTO `admin_logs` VALUES ('1', '1', '系统设置', '修改系统设置: 系统设置', '中', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"changes\": [\"系统设置\"], \"timestamp\": \"2025-07-28T09:20:47.352Z\", \"bannersCount\": 0, \"systemSettings\": {\"platformName\": \"抽奖平台\", \"maintenanceMode\": false, \"customerServiceQQ\": \"123456789\", \"maintenanceNotice\": \"系统维护中，请稍后再试...\", \"defaultMaxAttempts\": 3, \"customerServiceWechat\": \"service_wechat\"}, \"prizeConfigCount\": 0}', '2025-07-28 17:20:47');
INSERT INTO `admin_logs` VALUES ('2', '1', '系统设置', '修改系统设置: 系统设置', '中', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"changes\": [\"系统设置\"], \"timestamp\": \"2025-07-28T09:20:50.716Z\", \"bannersCount\": 0, \"systemSettings\": {\"platformName\": \"抽奖平台\", \"maintenanceMode\": false, \"customerServiceQQ\": \"123456789\", \"maintenanceNotice\": \"系统维护中，请稍后再试...\", \"defaultMaxAttempts\": 3, \"customerServiceWechat\": \"service_wechat\"}, \"prizeConfigCount\": 0}', '2025-07-28 17:20:50');
INSERT INTO `admin_logs` VALUES ('3', '1', '期次管理', '更新期次 2024年第1期 (第1期) 状态: 进行中 → 已结束', '中', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"periodId\": 1, \"newStatus\": \"已结束\", \"oldStatus\": \"进行中\", \"timestamp\": \"2025-07-28T09:39:51.024Z\", \"periodName\": \"2024年第1期\", \"periodNumber\": 1}', '2025-07-28 17:39:51');
INSERT INTO `admin_logs` VALUES ('4', '1', '期次管理', '创建新抽奖期次：第二期 (第2期)', '中', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"endTime\": \"2025-08-28T17:40\", \"drawTime\": \"2025-08-28T17:40\", \"startTime\": \"2025-07-28T17:40\", \"timestamp\": \"2025-07-28T09:46:34.211Z\", \"periodName\": \"第二期\", \"prizesCount\": 0, \"totalShares\": 1000, \"periodNumber\": 2, \"totalPrizeAmount\": 10000, \"maxAttemptsPerUser\": 1, \"actualDrawableAmount\": 3000}', '2025-07-28 17:46:34');

-- ----------------------------
-- Table structure for banners
-- ----------------------------
DROP TABLE IF EXISTS `banners`;
CREATE TABLE `banners` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `title` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `url` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `image_url` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `enabled` tinyint(1) DEFAULT '1',
  `sort_order` int DEFAULT '0',
  `description` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `start_time` datetime DEFAULT NULL,
  `end_time` datetime DEFAULT NULL,
  `click_count` int DEFAULT '0',
  `is_active` tinyint(1) DEFAULT '1',
  `created_at` datetime NOT NULL,
  `updated_at` datetime NOT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_enabled_sort` (`enabled`,`sort_order`),
  KEY `idx_active_time` (`is_active`,`start_time`,`end_time`)
) ENGINE=InnoDB AUTO_INCREMENT=10 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- ----------------------------
-- Records of banners
-- ----------------------------
INSERT INTO `banners` VALUES ('8', '首充优惠', 'https://example.com/first-deposit', 'https://example.com/banner1.jpg', '1', '1', '首次充值优惠活动', null, null, '0', '1', '2025-07-28 16:42:28', '2025-07-28 16:42:28');
INSERT INTO `banners` VALUES ('9', '首充优惠12', 'https://example.com/first-deposit', 'https://example.com/banner1.jpg', '1', '2', '', null, null, '0', '1', '2025-07-28 16:42:28', '2025-07-28 16:42:28');

-- ----------------------------
-- Table structure for lottery_periods
-- ----------------------------
DROP TABLE IF EXISTS `lottery_periods`;
CREATE TABLE `lottery_periods` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `period_name` varchar(100) COLLATE utf8mb4_general_ci NOT NULL,
  `period_number` int NOT NULL,
  `start_time` datetime NOT NULL,
  `end_time` datetime NOT NULL,
  `draw_time` datetime NOT NULL,
  `status` enum('未开始','进行中','已结束','已开奖') COLLATE utf8mb4_general_ci DEFAULT '未开始',
  `max_attempts_per_user` int DEFAULT '3',
  `total_participants` int DEFAULT '0',
  `total_attempts` int DEFAULT '0',
  `total_prize_amount` decimal(15,2) DEFAULT '0.00' COMMENT '本期抽奖总金额',
  `actual_drawable_amount` decimal(15,2) DEFAULT '0.00' COMMENT '实际可抽取金额',
  `total_shares` int DEFAULT '0' COMMENT '总份数',
  `settings` json DEFAULT NULL,
  `created_at` datetime NOT NULL,
  `updated_at` datetime NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- ----------------------------
-- Records of lottery_periods
-- ----------------------------
INSERT INTO `lottery_periods` VALUES ('1', '2024年第1期', '1', '2025-07-28 04:27:13', '2025-08-27 04:27:13', '2025-08-27 04:27:13', '已结束', '3', '0', '0', '0.00', '0.00', '0', null, '2025-07-28 04:27:13');
INSERT INTO `lottery_periods` VALUES ('2', '第二期', '2', '2025-07-28 17:40:00', '2025-08-28 17:40:00', '2025-08-28 17:40:00', '进行中', '1', '0', '0', '10000.00', '3000.00', '1000', null, '2025-07-28 17:46:33');

-- ----------------------------
-- Table structure for lottery_records
-- ----------------------------
DROP TABLE IF EXISTS `lottery_records`;
CREATE TABLE `lottery_records` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `period_id` bigint NOT NULL,
  `user_id` bigint NOT NULL,
  `platform_id` varchar(50) COLLATE utf8mb4_general_ci NOT NULL,
  `prize_level` int DEFAULT NULL,
  `prize_name` varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `prize_amount` decimal(10,2) DEFAULT '0.00',
  `is_winner` tinyint(1) DEFAULT '0',
  `draw_result` json DEFAULT NULL,
  `status` enum('待发放','已发放','已取消') COLLATE utf8mb4_general_ci DEFAULT '待发放',
  `drawn_at` datetime DEFAULT NULL,
  `created_at` datetime NOT NULL,
  `updated_at` datetime NOT NULL,
  PRIMARY KEY (`id`),
  KEY `period_id` (`period_id`),
  KEY `user_id` (`user_id`),
  CONSTRAINT `lottery_records_ibfk_61` FOREIGN KEY (`period_id`) REFERENCES `lottery_periods` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `lottery_records_ibfk_62` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- ----------------------------
-- Records of lottery_records
-- ----------------------------

-- ----------------------------
-- Table structure for prize_configs
-- ----------------------------
DROP TABLE IF EXISTS `prize_configs`;
CREATE TABLE `prize_configs` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `period_id` bigint NOT NULL,
  `prize_level` int NOT NULL,
  `prize_name` varchar(100) COLLATE utf8mb4_general_ci NOT NULL,
  `prize_amount` decimal(10,2) NOT NULL,
  `probability` decimal(5,4) NOT NULL,
  `max_winners` int DEFAULT '-1',
  `current_winners` int DEFAULT '0',
  `color_class` varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `sort_order` int DEFAULT '0',
  `is_active` tinyint(1) DEFAULT '1',
  `created_at` datetime NOT NULL,
  `updated_at` datetime NOT NULL,
  PRIMARY KEY (`id`),
  KEY `period_id` (`period_id`),
  CONSTRAINT `prize_configs_ibfk_1` FOREIGN KEY (`period_id`) REFERENCES `lottery_periods` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=19 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- ----------------------------
-- Records of prize_configs
-- ----------------------------
INSERT INTO `prize_configs` VALUES ('7', '1', '1', '特等奖', '5000.00', '0.0010', '-1', '0', 'bg-gradient-to-br from-red-500 to-red-600', '1', '1', '2025-07-28 16:16:47', '2025-07-28 16:16:47');
INSERT INTO `prize_configs` VALUES ('8', '1', '2', '一等奖', '1000.00', '0.0050', '-1', '0', 'bg-gradient-to-br from-purple-500 to-purple-600', '2', '1', '2025-07-28 16:16:47', '2025-07-28 16:16:47');
INSERT INTO `prize_configs` VALUES ('9', '1', '3', '二等奖', '500.00', '0.0100', '-1', '0', 'bg-gradient-to-br from-blue-500 to-blue-600', '3', '1', '2025-07-28 16:16:47', '2025-07-28 16:16:47');
INSERT INTO `prize_configs` VALUES ('10', '1', '4', '三等奖', '200.00', '0.0200', '-1', '0', 'bg-gradient-to-br from-green-500 to-green-600', '4', '1', '2025-07-28 16:16:47', '2025-07-28 16:16:47');
INSERT INTO `prize_configs` VALUES ('11', '1', '5', '四等奖', '100.00', '0.0300', '-1', '0', 'bg-gradient-to-br from-yellow-500 to-yellow-600', '5', '1', '2025-07-28 16:16:47', '2025-07-28 16:16:47');
INSERT INTO `prize_configs` VALUES ('12', '1', '6', '五等奖', '50.00', '0.0500', '-1', '0', 'bg-gradient-to-br from-orange-500 to-orange-600', '6', '1', '2025-07-28 16:16:47', '2025-07-28 16:16:47');
INSERT INTO `prize_configs` VALUES ('13', '1', '7', '六等奖', '20.00', '0.0800', '-1', '0', 'bg-gradient-to-br from-pink-500 to-pink-600', '7', '1', '2025-07-28 16:16:47', '2025-07-28 16:16:47');
INSERT INTO `prize_configs` VALUES ('14', '1', '8', '七等奖', '10.00', '0.1000', '-1', '0', 'bg-gradient-to-br from-indigo-500 to-indigo-600', '8', '1', '2025-07-28 16:16:47', '2025-07-28 16:16:47');
INSERT INTO `prize_configs` VALUES ('15', '1', '9', '八等奖', '5.00', '0.1500', '-1', '0', 'bg-gradient-to-br from-teal-500 to-teal-600', '9', '1', '2025-07-28 16:16:47', '2025-07-28 16:16:47');
INSERT INTO `prize_configs` VALUES ('16', '1', '10', '九等奖', '2.00', '0.2000', '-1', '0', 'bg-gradient-to-br from-cyan-500 to-cyan-600', '10', '1', '2025-07-28 16:16:47', '2025-07-28 16:16:47');
INSERT INTO `prize_configs` VALUES ('17', '1', '11', '十等奖', '1.00', '0.2500', '-1', '0', 'bg-gradient-to-br from-lime-500 to-lime-600', '11', '1', '2025-07-28 16:16:47', '2025-07-28 16:16:47');
INSERT INTO `prize_configs` VALUES ('18', '1', '12', '谢谢参与', '0.00', '0.1040', '-1', '0', 'bg-gradient-to-br from-gray-500 to-gray-600', '12', '1', '2025-07-28 16:16:47', '2025-07-28 16:16:47');

-- ----------------------------
-- Table structure for system_settings
-- ----------------------------
DROP TABLE IF EXISTS `system_settings`;
CREATE TABLE `system_settings` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `setting_key` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `setting_value` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci,
  `setting_type` enum('string','number','boolean','json') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT 'string',
  `description` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT '1',
  `created_at` datetime NOT NULL,
  `updated_at` datetime NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_setting_key` (`setting_key`)
) ENGINE=InnoDB AUTO_INCREMENT=7 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- ----------------------------
-- Records of system_settings
-- ----------------------------
INSERT INTO `system_settings` VALUES ('1', 'platform_name', '抽奖平台', 'string', '平台名称', '1', '2025-07-28 16:25:25', '2025-07-28 17:11:36');
INSERT INTO `system_settings` VALUES ('2', 'customer_service_qq', '123456789', 'string', '客服QQ号', '1', '2025-07-28 16:25:25', '2025-07-28 17:11:36');
INSERT INTO `system_settings` VALUES ('3', 'customer_service_wechat', 'service_wechat', 'string', '客服微信号', '1', '2025-07-28 16:25:25', '2025-07-28 17:11:37');
INSERT INTO `system_settings` VALUES ('4', 'default_max_attempts', '3', 'number', '默认抽奖次数', '1', '2025-07-28 16:25:25', '2025-07-28 17:20:46');
INSERT INTO `system_settings` VALUES ('5', 'maintenance_mode', 'false', 'boolean', '维护模式开关', '1', '2025-07-28 16:25:25', '2025-07-28 16:25:25');
INSERT INTO `system_settings` VALUES ('6', 'maintenance_notice', '系统维护中，请稍后再试...', 'string', '维护公告内容', '1', '2025-07-28 16:25:25', '2025-07-28 16:25:25');

-- ----------------------------
-- Table structure for users
-- ----------------------------
DROP TABLE IF EXISTS `users`;
CREATE TABLE `users` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `platform_id` varchar(50) COLLATE utf8mb4_general_ci NOT NULL,
  `username` varchar(100) COLLATE utf8mb4_general_ci NOT NULL,
  `password_hash` varchar(255) COLLATE utf8mb4_general_ci NOT NULL,
  `status` enum('正常','禁用') COLLATE utf8mb4_general_ci DEFAULT '正常',
  `total_winnings` decimal(12,2) DEFAULT '0.00',
  `created_at` datetime NOT NULL,
  `updated_at` datetime NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `platform_id` (`platform_id`),
  UNIQUE KEY `username` (`username`),
  UNIQUE KEY `platform_id_2` (`platform_id`),
  UNIQUE KEY `username_2` (`username`),
  UNIQUE KEY `platform_id_3` (`platform_id`),
  UNIQUE KEY `username_3` (`username`),
  UNIQUE KEY `platform_id_4` (`platform_id`),
  UNIQUE KEY `username_4` (`username`),
  UNIQUE KEY `platform_id_5` (`platform_id`),
  UNIQUE KEY `username_5` (`username`),
  UNIQUE KEY `platform_id_6` (`platform_id`),
  UNIQUE KEY `username_6` (`username`),
  UNIQUE KEY `platform_id_7` (`platform_id`),
  UNIQUE KEY `username_7` (`username`),
  UNIQUE KEY `platform_id_8` (`platform_id`),
  UNIQUE KEY `username_8` (`username`),
  UNIQUE KEY `platform_id_9` (`platform_id`),
  UNIQUE KEY `username_9` (`username`),
  UNIQUE KEY `platform_id_10` (`platform_id`),
  UNIQUE KEY `username_10` (`username`),
  UNIQUE KEY `platform_id_11` (`platform_id`),
  UNIQUE KEY `username_11` (`username`),
  UNIQUE KEY `platform_id_12` (`platform_id`),
  UNIQUE KEY `username_12` (`username`),
  UNIQUE KEY `platform_id_13` (`platform_id`),
  UNIQUE KEY `username_13` (`username`),
  UNIQUE KEY `platform_id_14` (`platform_id`),
  UNIQUE KEY `username_14` (`username`),
  UNIQUE KEY `platform_id_15` (`platform_id`),
  UNIQUE KEY `username_15` (`username`),
  UNIQUE KEY `platform_id_16` (`platform_id`),
  UNIQUE KEY `username_16` (`username`),
  UNIQUE KEY `platform_id_17` (`platform_id`),
  UNIQUE KEY `username_17` (`username`),
  UNIQUE KEY `platform_id_18` (`platform_id`),
  UNIQUE KEY `username_18` (`username`),
  UNIQUE KEY `platform_id_19` (`platform_id`),
  UNIQUE KEY `username_19` (`username`),
  UNIQUE KEY `platform_id_20` (`platform_id`),
  UNIQUE KEY `username_20` (`username`),
  UNIQUE KEY `platform_id_21` (`platform_id`),
  UNIQUE KEY `username_21` (`username`),
  UNIQUE KEY `platform_id_22` (`platform_id`),
  UNIQUE KEY `username_22` (`username`),
  UNIQUE KEY `platform_id_23` (`platform_id`),
  UNIQUE KEY `username_23` (`username`),
  UNIQUE KEY `platform_id_24` (`platform_id`),
  UNIQUE KEY `username_24` (`username`),
  UNIQUE KEY `platform_id_25` (`platform_id`),
  UNIQUE KEY `username_25` (`username`),
  UNIQUE KEY `platform_id_26` (`platform_id`),
  UNIQUE KEY `username_26` (`username`),
  UNIQUE KEY `platform_id_27` (`platform_id`),
  UNIQUE KEY `username_27` (`username`),
  UNIQUE KEY `platform_id_28` (`platform_id`),
  UNIQUE KEY `username_28` (`username`),
  UNIQUE KEY `platform_id_29` (`platform_id`),
  UNIQUE KEY `username_29` (`username`),
  UNIQUE KEY `platform_id_30` (`platform_id`),
  UNIQUE KEY `username_30` (`username`),
  UNIQUE KEY `platform_id_31` (`platform_id`),
  UNIQUE KEY `username_31` (`username`),
  UNIQUE KEY `platform_id_32` (`platform_id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- ----------------------------
-- Records of users
-- ----------------------------
INSERT INTO `users` VALUES ('1', 'TEST001', 'testuser', '$2b$10$GojvO2yEvZYd/CFu6aS3MedpMjXUnAyUSRccqEorgAvrsXLWr.Y0i', '正常', '0.00', '2025-07-28 04:27:15', '2025-07-28 04:27:15');

-- ----------------------------
-- Table structure for user_lottery_limits
-- ----------------------------
DROP TABLE IF EXISTS `user_lottery_limits`;
CREATE TABLE `user_lottery_limits` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `period_id` bigint NOT NULL,
  `user_id` bigint NOT NULL,
  `platform_id` varchar(50) COLLATE utf8mb4_general_ci NOT NULL,
  `max_attempts` int NOT NULL,
  `used_attempts` int DEFAULT '0',
  `last_attempt_at` datetime DEFAULT NULL,
  `created_at` datetime NOT NULL,
  `updated_at` datetime NOT NULL,
  PRIMARY KEY (`id`),
  KEY `period_id` (`period_id`),
  KEY `user_id` (`user_id`),
  CONSTRAINT `user_lottery_limits_ibfk_59` FOREIGN KEY (`period_id`) REFERENCES `lottery_periods` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `user_lottery_limits_ibfk_60` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- ----------------------------
-- Records of user_lottery_limits
-- ----------------------------
