-- 检查用户个人抽奖率配置数据表
-- 执行此脚本可以查看数据表结构和数据

-- 1. 检查 users 表结构（包含 custom_lottery_enabled 字段）
DESCRIBE users;

-- 2. 检查 user_prize_configs 表结构
DESCRIBE user_prize_configs;

-- 3. 查看 users 表中的个人抽奖率开关状态
SELECT 
    id,
    username,
    platform_id,
    custom_lottery_enabled,
    status,
    created_at
FROM users 
ORDER BY id;

-- 4. 查看 user_prize_configs 表中的配置数据
SELECT 
    upc.id,
    upc.user_id,
    u.username,
    u.platform_id,
    pc.prize_name,
    pc.prize_amount,
    pc.probability as global_probability,
    upc.custom_probability,
    upc.is_enabled,
    upc.created_at
FROM user_prize_configs upc
LEFT JOIN users u ON upc.user_id = u.id
LEFT JOIN prize_configs pc ON upc.prize_config_id = pc.id
ORDER BY upc.user_id, pc.id;

-- 5. 统计每个用户的配置数量
SELECT 
    u.id,
    u.username,
    u.platform_id,
    u.custom_lottery_enabled,
    COUNT(upc.id) as config_count
FROM users u
LEFT JOIN user_prize_configs upc ON u.id = upc.user_id
GROUP BY u.id, u.username, u.platform_id, u.custom_lottery_enabled
ORDER BY u.id;

-- 6. 查看启用个人抽奖率的用户
SELECT 
    u.id,
    u.username,
    u.platform_id,
    u.custom_lottery_enabled,
    COUNT(upc.id) as total_configs,
    SUM(CASE WHEN upc.is_enabled = 1 THEN 1 ELSE 0 END) as enabled_configs
FROM users u
LEFT JOIN user_prize_configs upc ON u.id = upc.user_id
WHERE u.custom_lottery_enabled = 1
GROUP BY u.id, u.username, u.platform_id, u.custom_lottery_enabled
ORDER BY u.id;

-- 7. 检查数据表索引
SHOW INDEX FROM user_prize_configs;

-- 8. 检查外键约束
SELECT 
    CONSTRAINT_NAME,
    TABLE_NAME,
    COLUMN_NAME,
    REFERENCED_TABLE_NAME,
    REFERENCED_COLUMN_NAME
FROM information_schema.KEY_COLUMN_USAGE 
WHERE TABLE_SCHEMA = DATABASE() 
AND TABLE_NAME = 'user_prize_configs'
AND REFERENCED_TABLE_NAME IS NOT NULL;
