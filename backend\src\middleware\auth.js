const jwt = require('jsonwebtoken')
const { Admin } = require('../models')

const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key'

// 用户JWT认证中间件
const authenticateToken = (req, res, next) => {
  const authHeader = req.headers['authorization']
  const token = authHeader && authHeader.split(' ')[1]

  if (!token) {
    return res.status(401).json({ error: '需要登录' })
  }

  jwt.verify(token, JWT_SECRET, (err, user) => {
    if (err) {
      console.log('❌ JWT验证失败:', err.message)
      if (err.name === 'TokenExpiredError') {
        return res.status(401).json({ error: 'token已过期', code: 'TOKEN_EXPIRED' })
      }
      return res.status(403).json({ error: '无效的token' })
    }
    console.log('✅ JWT验证成功:', {
      userId: user.userId,
      username: user.username,
      platformId: user.platformId
    })
    req.user = user
    next()
  })
}

// 管理员JWT认证中间件
const authenticateAdmin = async (req, res, next) => {
  const authHeader = req.headers['authorization']
  const token = authHeader && authHeader.split(' ')[1]

  if (!token) {
    return res.status(401).json({ error: '需要管理员登录' })
  }

  try {
    const decoded = jwt.verify(token, JWT_SECRET)
    
    // 验证是否为管理员token
    if (!decoded.isAdmin) {
      return res.status(403).json({ error: '需要管理员权限' })
    }

    // 验证管理员是否存在且状态正常
    const admin = await Admin.findByPk(decoded.adminId)
    if (!admin || admin.status !== '正常') {
      return res.status(403).json({ error: '管理员账户异常' })
    }

    req.admin = {
      id: admin.id,
      username: admin.username,
      role: admin.role
    }
    next()
  } catch (err) {
    return res.status(403).json({ error: '无效的管理员token' })
  }
}

// 获取客户端IP地址
const getClientIP = (req) => {
  return req.headers['x-forwarded-for'] || 
         req.connection.remoteAddress || 
         req.socket.remoteAddress ||
         (req.connection.socket ? req.connection.socket.remoteAddress : null) ||
         '127.0.0.1'
}

module.exports = {
  authenticateToken,
  authenticateAdmin,
  getClientIP,
  JWT_SECRET
}
