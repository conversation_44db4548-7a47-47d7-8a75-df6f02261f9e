<template>
  <div class="admin-lottery-periods min-h-screen bg-gradient-to-br from-gray-900 via-black to-gray-800 p-6">
    <!-- 页面头部 -->
    <div class="mb-8">
      <div class="flex items-center justify-between">
        <div>
          <h2 class="text-3xl font-bold text-white mb-2 flex items-center">
            <el-icon class="mr-3 text-gold-400" size="32"><Calendar /></el-icon>
            期次管理
          </h2>
          <p class="text-gray-400">管理抽奖期次设置和状态</p>
        </div>
        <div class="flex items-center space-x-4">
          <div class="px-4 py-2 bg-gradient-to-r from-gold-500/20 to-gold-600/20 border border-gold-500/30 rounded-lg">
            <span class="text-gold-400 text-sm font-medium">总期次数: {{ filteredPeriods.length }}</span>
          </div>
          <button
            @click="handleRefresh"
            class="px-4 py-2 bg-blue-500/20 hover:bg-blue-500/30 border border-blue-500/30 text-blue-400 hover:text-blue-300 rounded-lg transition-all duration-200 flex items-center space-x-2"
          >
            <el-icon><Refresh /></el-icon>
            <span>刷新数据</span>
          </button>
        </div>
      </div>
    </div>

    <!-- 操作栏 -->
    <div class="bg-gradient-to-br from-gray-800 to-gray-900 border border-gold-500/30 rounded-xl shadow-xl p-6 mb-6">
      <div class="flex justify-between items-center">
        <div class="flex gap-4 items-center">
          <select
            v-model="statusFilter"
            class="px-4 py-3 bg-gray-700 border border-gray-600 text-white rounded-lg focus:outline-none focus:border-gold-500 min-w-[150px]"
          >
            <option value="">全部状态</option>
            <option value="未开始">未开始</option>
            <option value="进行中">进行中</option>
            <option value="已结束">已结束</option>
          </select>
        </div>

        <button
          @click="showCreateDialog"
          class="px-6 py-3 bg-gradient-to-r from-gold-500 to-gold-600 hover:from-gold-600 hover:to-gold-700 text-black font-medium rounded-lg transition-all duration-200 flex items-center space-x-2"
        >
          <el-icon><Plus /></el-icon>
          <span>创建新期次</span>
        </button>
      </div>
    </div>

    <!-- 期次列表 -->
    <div class="bg-gradient-to-br from-gray-800 to-gray-900 border border-gold-500/30 rounded-xl shadow-xl overflow-hidden">
      <!-- 表格头部 -->
      <div class="px-6 py-4 border-b border-gray-700">
        <h3 class="text-lg font-semibold text-white flex items-center">
          <el-icon class="mr-2 text-gold-400"><List /></el-icon>
          期次列表
        </h3>
      </div>

      <!-- 加载状态 -->
      <div v-if="isLoading" class="flex items-center justify-center py-12">
        <div class="text-center">
          <div class="w-8 h-8 border-2 border-gold-500 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p class="text-gray-400">加载中...</p>
        </div>
      </div>

      <!-- 表格内容 -->
      <div v-else-if="filteredPeriods.length > 0" class="overflow-x-auto">
        <table class="w-full">
          <thead class="bg-gray-700/50">
            <tr>
              <th class="px-6 py-4 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">期次ID</th>
              <th class="px-6 py-4 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">期次信息</th>
              <th class="px-6 py-4 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">状态</th>
              <th class="px-6 py-4 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">抽奖时间</th>
              <th class="px-6 py-4 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">金额设置</th>
              <th class="px-6 py-4 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">限制次数</th>
              <th class="px-6 py-4 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">参与统计</th>
              <th class="px-6 py-4 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">操作</th>
            </tr>
          </thead>
          <tbody class="divide-y divide-gray-700">
            <tr
              v-for="period in filteredPeriods"
              :key="period.id"
              class="hover:bg-gray-700/30 transition-colors"
            >
              <!-- 期次ID -->
              <td class="px-6 py-4 whitespace-nowrap">
                <span class="text-gold-400 font-medium">#{{ period.id }}</span>
              </td>

              <!-- 期次信息 -->
              <td class="px-6 py-4 whitespace-nowrap">
                <div>
                  <div class="text-white font-medium">{{ period.periodName }}</div>
                  <div class="text-gray-400 text-sm">第{{ period.periodNumber }}期</div>
                </div>
              </td>

              <!-- 状态 -->
              <td class="px-6 py-4 whitespace-nowrap">
                <span :class="getStatusColor(period.status)" class="px-2 py-1 border rounded text-sm font-medium">
                  {{ period.status }}
                </span>
              </td>

              <!-- 抽奖时间 -->
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm space-y-1">
                  <div class="text-gray-300">
                    <span class="text-gray-500">开始:</span> {{ formatDate(period.startTime) }}
                  </div>
                  <div class="text-gray-300">
                    <span class="text-gray-500">结束:</span> {{ formatDate(period.endTime) }}
                  </div>
                </div>
              </td>

              <!-- 金额设置 -->
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm space-y-1">
                  <div class="text-gray-300">
                    <span class="text-gray-500">总金额:</span> <span class="text-gold-400 font-medium">¥{{ (period.totalPrizeAmount || 0).toLocaleString() }}</span>
                  </div>
                  <div class="text-gray-300">
                    <span class="text-gray-500">可抽取:</span> <span class="text-green-400 font-medium">¥{{ (period.actualDrawableAmount || 0).toLocaleString() }}</span>
                  </div>
                  <div class="text-gray-300">
                    <span class="text-gray-500">份数:</span> <span class="text-blue-400 font-medium">{{ (period.totalShares || 0).toLocaleString() }}</span>
                  </div>
                </div>
              </td>

              <!-- 限制次数 -->
              <td class="px-6 py-4 whitespace-nowrap text-center">
                <span class="text-blue-400 font-medium">{{ period.maxAttemptsPerUser }}次</span>
              </td>

              <!-- 参与统计 -->
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm space-y-1">
                  <div class="text-gray-300">
                    参与人数: <span class="text-green-400 font-medium">{{ period.totalParticipants || 0 }}</span>
                  </div>
                  <div class="text-gray-300">
                    总次数: <span class="text-blue-400 font-medium">{{ period.totalAttempts || 0 }}</span>
                  </div>
                </div>
              </td>

              <!-- 操作 -->
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="flex space-x-2">
                  <button
                    v-if="period.status === '未开始'"
                    @click="handleUpdateStatus(period.id, '进行中')"
                    class="px-3 py-1 bg-green-500/20 hover:bg-green-500/30 border border-green-500/30 text-green-400 hover:text-green-300 rounded transition-all text-sm"
                  >
                    开始
                  </button>
                  <button
                    v-if="period.status === '进行中'"
                    @click="handleUpdateStatus(period.id, '已结束')"
                    class="px-3 py-1 bg-yellow-500/20 hover:bg-yellow-500/30 border border-yellow-500/30 text-yellow-400 hover:text-yellow-300 rounded transition-all text-sm"
                  >
                    结束
                  </button>
                  <button
                    @click="handleViewDetails(period)"
                    class="px-3 py-1 bg-blue-500/20 hover:bg-blue-500/30 border border-blue-500/30 text-blue-400 hover:text-blue-300 rounded transition-all text-sm"
                  >
                    详情
                  </button>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      <!-- 空状态 -->
      <div v-else class="flex items-center justify-center py-12">
        <div class="text-center">
          <el-icon size="64" class="text-gray-600 mb-4"><Calendar /></el-icon>
          <p class="text-gray-400 text-lg mb-2">暂无期次数据</p>
          <p class="text-gray-500 text-sm">请创建第一个抽奖期次</p>
        </div>
      </div>
    </div>

    <!-- 创建期次对话框 -->
    <div v-if="createDialogVisible" class="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
      <div class="bg-gradient-to-br from-gray-800 to-gray-900 border border-gold-500/30 rounded-xl shadow-2xl w-full max-w-2xl max-h-[90vh] overflow-y-auto">
        <!-- 对话框头部 -->
        <div class="flex items-center justify-between p-6 border-b border-gray-700">
          <h3 class="text-xl font-semibold text-white flex items-center">
            <el-icon class="mr-3 text-gold-400" size="24"><Plus /></el-icon>
            创建新期次
          </h3>
          <button
            @click="createDialogVisible = false"
            class="p-2 text-gray-400 hover:text-white hover:bg-gray-700 rounded-lg transition-all"
          >
            <el-icon size="20"><Close /></el-icon>
          </button>
        </div>

        <!-- 表单内容 -->
        <div class="p-6">
          <div class="space-y-6">
            <div>
              <label class="block text-sm font-medium text-gray-400 mb-2">期次名称</label>
              <input
                v-model="createForm.periodName"
                type="text"
                placeholder="例如：2024年第1期"
                class="w-full px-4 py-3 bg-gray-700 border border-gray-600 text-white placeholder-gray-400 rounded-lg focus:outline-none focus:border-gold-500 focus:ring-1 focus:ring-gold-500 transition-all"
              />
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-400 mb-2">期次编号</label>
              <input
                v-model="createForm.periodNumber"
                type="number"
                min="1"
                placeholder="期次编号"
                class="w-full px-4 py-3 bg-gray-700 border border-gray-600 text-white placeholder-gray-400 rounded-lg focus:outline-none focus:border-gold-500 focus:ring-1 focus:ring-gold-500 transition-all"
              />
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label class="block text-sm font-medium text-gray-400 mb-2">开始时间</label>
                <input
                  v-model="createForm.startTime"
                  type="datetime-local"
                  class="w-full px-4 py-3 bg-gray-700 border border-gray-600 text-white rounded-lg focus:outline-none focus:border-gold-500 focus:ring-1 focus:ring-gold-500 transition-all"
                />
              </div>

              <div>
                <label class="block text-sm font-medium text-gray-400 mb-2">结束时间</label>
                <input
                  v-model="createForm.endTime"
                  type="datetime-local"
                  class="w-full px-4 py-3 bg-gray-700 border border-gray-600 text-white rounded-lg focus:outline-none focus:border-gold-500 focus:ring-1 focus:ring-gold-500 transition-all"
                />
              </div>
            </div>

            <!-- 金额设置 -->
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label class="block text-sm font-medium text-gray-400 mb-2">本期抽奖总金额</label>
                <input
                  v-model="createForm.totalPrizeAmount"
                  type="number"
                  min="0"
                  step="0.01"
                  placeholder="总金额"
                  class="w-full px-4 py-3 bg-gray-700 border border-gray-600 text-white placeholder-gray-400 rounded-lg focus:outline-none focus:border-gold-500 focus:ring-1 focus:ring-gold-500 transition-all"
                />
              </div>

              <div>
                <label class="block text-sm font-medium text-gray-400 mb-2">实际可抽取金额</label>
                <input
                  v-model="createForm.actualDrawableAmount"
                  type="number"
                  min="0"
                  step="0.01"
                  placeholder="可抽取金额"
                  class="w-full px-4 py-3 bg-gray-700 border border-gray-600 text-white placeholder-gray-400 rounded-lg focus:outline-none focus:border-gold-500 focus:ring-1 focus:ring-gold-500 transition-all"
                />
              </div>

              <div>
                <label class="block text-sm font-medium text-gray-400 mb-2">总份数</label>
                <input
                  v-model="createForm.totalShares"
                  type="number"
                  min="0"
                  placeholder="可参与抽取的份数"
                  class="w-full px-4 py-3 bg-gray-700 border border-gray-600 text-white placeholder-gray-400 rounded-lg focus:outline-none focus:border-gold-500 focus:ring-1 focus:ring-gold-500 transition-all"
                />
              </div>
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-400 mb-2">每人限制次数</label>
              <input
                v-model="createForm.maxAttemptsPerUser"
                type="number"
                min="1"
                max="10"
                placeholder="每人最多抽奖次数"
                class="w-full px-4 py-3 bg-gray-700 border border-gray-600 text-white placeholder-gray-400 rounded-lg focus:outline-none focus:border-gold-500 focus:ring-1 focus:ring-gold-500 transition-all"
              />
            </div>
          </div>

          <!-- 操作按钮 -->
          <div class="flex justify-end space-x-3 mt-8 pt-6 border-t border-gray-700">
            <button
              @click="createDialogVisible = false"
              class="px-6 py-2 bg-gray-600 hover:bg-gray-500 text-white font-medium rounded-lg transition-all"
            >
              取消
            </button>
            <button
              @click="handleCreatePeriod"
              class="px-6 py-2 bg-gradient-to-r from-gold-500 to-gold-600 hover:from-gold-600 hover:to-gold-700 text-black font-medium rounded-lg transition-all"
            >
              创建期次
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- 期次详情对话框 -->
    <div v-if="detailDialogVisible" class="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
      <div class="bg-gradient-to-br from-gray-800 to-gray-900 border border-gold-500/30 rounded-xl shadow-2xl w-full max-w-2xl max-h-[90vh] overflow-y-auto">
        <!-- 对话框头部 -->
        <div class="flex items-center justify-between p-6 border-b border-gray-700">
          <h3 class="text-xl font-semibold text-white flex items-center">
            <el-icon class="mr-3 text-gold-400" size="24"><Calendar /></el-icon>
            期次详情
          </h3>
          <button
            @click="detailDialogVisible = false"
            class="p-2 text-gray-400 hover:text-white hover:bg-gray-700 rounded-lg transition-all"
          >
            <el-icon size="20"><Close /></el-icon>
          </button>
        </div>

        <!-- 详情内容 -->
        <div v-if="selectedPeriod" class="p-6">
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div class="space-y-4">
              <div>
                <label class="block text-sm font-medium text-gray-400 mb-2">期次ID</label>
                <p class="text-white font-medium">#{{ selectedPeriod.id }}</p>
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-400 mb-2">期次名称</label>
                <p class="text-white font-medium">{{ selectedPeriod.periodName }}</p>
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-400 mb-2">期次编号</label>
                <p class="text-blue-400 font-medium">第{{ selectedPeriod.periodNumber }}期</p>
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-400 mb-2">状态</label>
                <span :class="getStatusColor(selectedPeriod.status)" class="px-3 py-1 border rounded text-sm font-medium">
                  {{ selectedPeriod.status }}
                </span>
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-400 mb-2">每人限制次数</label>
                <p class="text-blue-400 font-medium">{{ selectedPeriod.maxAttemptsPerUser }}次</p>
              </div>
            </div>

            <div class="space-y-4">
              <div>
                <label class="block text-sm font-medium text-gray-400 mb-2">开始时间</label>
                <p class="text-gray-300">{{ formatDate(selectedPeriod.startTime) }}</p>
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-400 mb-2">结束时间</label>
                <p class="text-gray-300">{{ formatDate(selectedPeriod.endTime) }}</p>
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-400 mb-2">参与人数</label>
                <p class="text-green-400 font-medium">{{ selectedPeriod.totalParticipants || 0 }}人</p>
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-400 mb-2">总抽奖次数</label>
                <p class="text-green-400 font-medium">{{ selectedPeriod.totalAttempts || 0 }}次</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Calendar, Plus, Close, List, Refresh } from '@element-plus/icons-vue'
import { useAdminStore } from '../../stores/admin'

const adminStore = useAdminStore()

// 筛选
const statusFilter = ref('')
const isLoading = ref(false)

// 对话框
const createDialogVisible = ref(false)
const detailDialogVisible = ref(false)
const selectedPeriod = ref(null)

// 创建表单
const createForm = ref({
  periodName: '',
  periodNumber: 1,
  startTime: '',
  endTime: '',
  maxAttemptsPerUser: 3,
  totalPrizeAmount: 0,
  actualDrawableAmount: 0,
  totalShares: 0
})



// 过滤后的期次列表
const filteredPeriods = computed(() => {
  // 确保 periods 是数组
  const periodsArray = Array.isArray(adminStore.lotteryPeriods) ? adminStore.lotteryPeriods : []
  let periods = [...periodsArray]

  if (statusFilter.value) {
    periods = periods.filter(period => period.status === statusFilter.value)
  }

  return periods.sort((a, b) => b.periodNumber - a.periodNumber)
})

// 格式化日期
const formatDate = (dateString: string) => {
  if (!dateString) return '未设置'
  return new Date(dateString).toLocaleString('zh-CN')
}

// 获取状态颜色类
const getStatusColor = (status: string) => {
  switch (status) {
    case '进行中': return 'bg-green-500/20 border-green-500/30 text-green-400'
    case '未开始': return 'bg-blue-500/20 border-blue-500/30 text-blue-400'
    case '已结束': return 'bg-gray-500/20 border-gray-500/30 text-gray-400'
    default: return 'bg-gray-500/20 border-gray-500/30 text-gray-400'
  }
}

// 显示创建对话框
const showCreateDialog = () => {
  // 重置表单
  const periodsArray = Array.isArray(adminStore.lotteryPeriods) ? adminStore.lotteryPeriods : []
  const maxPeriodNumber = periodsArray.length > 0
    ? Math.max(...periodsArray.map(p => p.periodNumber))
    : 0

  createForm.value = {
    periodName: '',
    periodNumber: maxPeriodNumber + 1,
    startTime: '',
    endTime: '',
    maxAttemptsPerUser: 3,
    totalPrizeAmount: 0,
    actualDrawableAmount: 0,
    totalShares: 0
  }
  createDialogVisible.value = true
}

// 刷新
const handleRefresh = async () => {
  isLoading.value = true
  try {
    await loadPeriods()
    ElMessage.success('期次列表已刷新')
  } finally {
    isLoading.value = false
  }
}

// 更新期次状态
const handleUpdateStatus = async (periodId: number, status: string) => {
  try {
    await ElMessageBox.confirm(
      `确定要将期次状态更新为"${status}"吗？`,
      '确认操作',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )

    // 调用API更新状态
    const result = await adminStore.updatePeriodStatus(periodId, status)
    if (result.success) {
      ElMessage.success('期次状态更新成功')
    } else {
      ElMessage.error(result.message || '更新期次状态失败')
    }
  } catch {
    // 用户取消
  }
}

// 创建期次
const handleCreatePeriod = async () => {
  try {
    // 简单验证
    if (!createForm.value.periodName || !createForm.value.startTime || !createForm.value.endTime) {
      ElMessage.error('请填写完整的期次信息')
      return
    }

    if (new Date(createForm.value.endTime) <= new Date(createForm.value.startTime)) {
      ElMessage.error('结束时间必须晚于开始时间')
      return
    }

    // 调用API创建期次
    const result = await adminStore.createLotteryPeriod(createForm.value)
    if (result.success) {
      ElMessage.success('期次创建成功')
      createDialogVisible.value = false
    } else {
      ElMessage.error(result.message || '创建期次失败')
    }
  } catch (error) {
    console.error('创建期次失败:', error)
    ElMessage.error('创建期次失败')
  }
}

// 查看期次详情
const handleViewDetails = (period: any) => {
  selectedPeriod.value = period
  detailDialogVisible.value = true
}

// 加载期次列表
const loadPeriods = async () => {
  try {
    const result = await adminStore.fetchLotteryPeriods()
    if (!result.success) {
      ElMessage.error(result.message || '加载期次列表失败')
    }
  } catch (error) {
    ElMessage.error('加载期次列表失败')
  }
}

onMounted(() => {
  loadPeriods()
})
</script>
