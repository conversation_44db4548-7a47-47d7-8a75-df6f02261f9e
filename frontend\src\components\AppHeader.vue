<template>
  <!-- 顶部导航栏 -->
  <header class="bg-gradient-to-r from-black to-gray-900 border-b border-gold-500/30 sticky top-0 z-50 backdrop-blur-sm">
    <div class="container mx-auto px-4 py-4">
      <div class="flex items-center justify-between">
        <!-- Lo<PERSON>和标题 -->
        <div class="flex items-center space-x-4">
          <div class="w-12 h-12 bg-gradient-to-br from-gold-400 to-gold-600 rounded-xl flex items-center justify-center shadow-lg">
            <span class="text-2xl font-bold text-black">🎰</span>
          </div>
          <div>
            <h1 class="text-2xl font-bold text-gold-400">澳门金沙</h1>
            <p class="text-sm text-gray-400">官方抽奖平台</p>
          </div>
        </div>

        <!-- 导航菜单 -->
        <nav class="hidden md:flex items-center space-x-8">
          <router-link 
            to="/" 
            class="text-gray-300 hover:text-gold-400 transition-colors duration-200 flex items-center space-x-2"
            :class="{ 'text-gold-400': $route.path === '/' }"
          >
            <span>🏠</span>
            <span>首页</span>
          </router-link>
          <router-link 
            to="/lottery" 
            class="text-gray-300 hover:text-gold-400 transition-colors duration-200 flex items-center space-x-2"
            :class="{ 'text-gold-400': $route.path === '/lottery' }"
          >
            <span>🎲</span>
            <span>抽奖</span>
          </router-link>
          <router-link
            to="/records"
            class="text-gray-300 hover:text-gold-400 transition-colors duration-200 flex items-center space-x-2"
            :class="{ 'text-gold-400': $route.path === '/records' }"
          >
            <span>📊</span>
            <span>记录</span>
          </router-link>
          <router-link
            to="/rules"
            class="text-gray-300 hover:text-gold-400 transition-colors duration-200 flex items-center space-x-2"
            :class="{ 'text-gold-400': $route.path === '/rules' }"
          >
            <span>📋</span>
            <span>活动规则</span>
          </router-link>

        </nav>

        <!-- 用户信息和操作 -->
        <div class="flex items-center space-x-4">
          <!-- 用户信息 -->
          <div v-if="authStore.isAuthenticated" class="hidden md:flex items-center space-x-3">
            <div class="text-right">
              <div class="text-sm font-medium text-gold-400">{{ authStore.user?.username }}</div>
              <div class="text-xs text-gray-400">ID: {{ authStore.user?.platformId || 'N/A' }}</div>
            </div>
            <div class="w-10 h-10 bg-gradient-to-br from-gold-400 to-gold-600 rounded-full flex items-center justify-center">
              <span class="text-sm font-bold text-black">{{ authStore.user?.username?.charAt(0).toUpperCase() }}</span>
            </div>
          </div>

          <!-- 登录/退出按钮 -->
          <div class="flex items-center space-x-2">
            <button 
              v-if="!authStore.isAuthenticated"
              @click="handleLogin"
              class="bg-gradient-to-r from-gold-500 to-gold-600 text-black px-6 py-2 rounded-lg font-medium hover:from-gold-400 hover:to-gold-500 transition-all duration-200 shadow-lg"
            >
              登录
            </button>
            <button 
              v-else
              @click="handleLogout"
              class="bg-gradient-to-r from-gray-600 to-gray-700 text-white px-4 py-2 rounded-lg font-medium hover:from-gray-500 hover:to-gray-600 transition-all duration-200"
            >
              退出
            </button>
          </div>

          <!-- 移动端菜单按钮 -->
          <button 
            @click="toggleMobileMenu"
            class="md:hidden text-gold-400 hover:text-gold-300 transition-colors"
          >
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
            </svg>
          </button>
        </div>
      </div>

      <!-- 移动端菜单 -->
      <div v-if="showMobileMenu" class="md:hidden mt-4 pb-4 border-t border-gold-500/30">
        <nav class="flex flex-col space-y-3 mt-4">
          <router-link 
            to="/" 
            class="text-gray-300 hover:text-gold-400 transition-colors duration-200 flex items-center space-x-2 py-2"
            :class="{ 'text-gold-400': $route.path === '/' }"
            @click="showMobileMenu = false"
          >
            <span>🏠</span>
            <span>首页</span>
          </router-link>
          <router-link 
            to="/lottery" 
            class="text-gray-300 hover:text-gold-400 transition-colors duration-200 flex items-center space-x-2 py-2"
            :class="{ 'text-gold-400': $route.path === '/lottery' }"
            @click="showMobileMenu = false"
          >
            <span>🎲</span>
            <span>抽奖</span>
          </router-link>
          <router-link to="/records" class="text-gray-300 hover:text-gold-400 transition-colors duration-200 flex items-center space-x-2 py-2">
            <span>📊</span>
            <span>记录</span>
          </router-link>
          <router-link to="/rules" class="text-gray-300 hover:text-gold-400 transition-colors duration-200 flex items-center space-x-2 py-2">
            <span>🎁</span>
            <span>奖品</span>
          </router-link>
          
          <!-- 移动端用户信息 -->
          <div v-if="authStore.isAuthenticated" class="pt-3 border-t border-gold-500/30">
            <div class="flex items-center space-x-3 py-2">
              <div class="w-8 h-8 bg-gradient-to-br from-gold-400 to-gold-600 rounded-full flex items-center justify-center">
                <span class="text-sm font-bold text-black">{{ authStore.user?.username?.charAt(0).toUpperCase() }}</span>
              </div>
              <div>
                <div class="text-sm font-medium text-gold-400">{{ authStore.user?.username }}</div>
                <div class="text-xs text-gray-400">ID: {{ authStore.user?.platformId || 'N/A' }}</div>
              </div>
            </div>
          </div>
        </nav>
      </div>
    </div>
  </header>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { ElMessage } from 'element-plus'

const router = useRouter()
const authStore = useAuthStore()
const showMobileMenu = ref(false)

const toggleMobileMenu = () => {
  showMobileMenu.value = !showMobileMenu.value
}

const handleLogin = () => {
  router.push('/login')
}

const handleLogout = async () => {
  await authStore.logout()
  ElMessage.success('已退出登录')
  router.push('/')
}
</script>
