import { ref } from 'vue'
import { defineStore } from 'pinia'
import { adminAPI } from '../api/config'

interface AdminStats {
  totalUsers: number
  totalLotteryRecords: number
  totalWinnings: number
  todayParticipants: number
}

interface User {
  id: number
  platformId: string
  username: string
  status: string
  totalWinnings: number
  createdAt: string
}

interface LotteryRecord {
  id: number
  periodId: number
  userId: number
  platformId: string
  prizeLevel: number
  prizeName: string
  prizeAmount: number
  isWinner: boolean
  status: string
  distributedAt?: string
  distributedBy?: number
  distributionRemark?: string
  drawnAt: string
  user: {
    id: number
    platformId: string
    username: string
  }
  period: {
    id: number
    periodName: string
  }
}

interface LotteryPeriod {
  id: number
  periodName: string
  periodNumber: number
  startTime: string
  endTime: string
  drawTime: string
  status: string
  maxAttemptsPerUser: number
  totalParticipants: number
  totalAttempts: number
}

interface AdminUser {
  id: number
  username: string
  email?: string
  role: string
  status: string
  lastLoginAt?: string
  lastLoginIp?: string
  createdAt: string
  updatedAt?: string
}

export const useAdminStore = defineStore('admin', () => {
  // 状态
  const stats = ref<AdminStats | null>(null)
  const users = ref<User[]>([])
  const lotteryRecords = ref<LotteryRecord[]>([])
  const lotteryPeriods = ref<LotteryPeriod[]>([])
  const admins = ref<AdminUser[]>([])
  const systemSettings = ref<any>(null)
  const prizeConfig = ref<any[]>([])
  const banners = ref<any[]>([])
  const isLoading = ref(false)

  // 获取统计数据
  const fetchStats = async () => {
    isLoading.value = true
    try {
      const response = await adminAPI.getDashboardStats()
      if (response.data.success) {
        stats.value = response.data.data
        return { success: true }
      } else {
        return { success: false, message: response.data.error || '获取统计数据失败' }
      }
    } catch (error: any) {
      console.error('获取统计数据失败:', error)
      return {
        success: false,
        message: error.response?.data?.error || '获取统计数据失败'
      }
    } finally {
      isLoading.value = false
    }
  }

  // 获取最近活动
  const fetchRecentActivities = async (limit = 10) => {
    try {
      const response = await adminAPI.getRecentActivities(limit)
      if (response.data.success) {
        return { success: true, data: response.data.data }
      } else {
        return { success: false, message: response.data.error || '获取最近活动失败' }
      }
    } catch (error: any) {
      console.error('获取最近活动失败:', error)
      return {
        success: false,
        message: error.response?.data?.error || '获取最近活动失败'
      }
    }
  }

  // 获取用户列表
  const fetchUsers = async () => {
    isLoading.value = true
    try {
      const response = await adminAPI.getUsers()
      if (response.data.success) {
        users.value = response.data.data.users
        return { success: true }
      } else {
        return {
          success: false,
          message: response.data.error || '获取用户列表失败'
        }
      }
    } catch (error: any) {
      console.error('获取用户列表失败:', error)
      return {
        success: false,
        message: error.response?.data?.error || '获取用户列表失败'
      }
    } finally {
      isLoading.value = false
    }
  }

  // 获取抽奖记录
  const fetchLotteryRecords = async (params?: { page?: number; limit?: number; periodId?: number; isWinner?: boolean; startDate?: string; endDate?: string }) => {
    isLoading.value = true
    try {
      const response = await adminAPI.getLotteryRecords(params)
      if (response.data.success) {
        lotteryRecords.value = response.data.data?.records || []
        return {
          success: true,
          data: response.data.data
        }
      } else {
        throw new Error(response.data.error || '获取抽奖记录失败')
      }
    } catch (error: any) {
      console.error('获取抽奖记录失败:', error)
      return {
        success: false,
        message: error.response?.data?.error || error.message || '获取抽奖记录失败'
      }
    } finally {
      isLoading.value = false
    }
  }

  // 发放奖品
  const distributePrize = async (recordId: number, remark: string = '') => {
    try {
      const response = await adminAPI.distributePrize(recordId, remark)
      if (response.data.success) {
        return {
          success: true,
          data: response.data.data,
          message: response.data.message
        }
      } else {
        return {
          success: false,
          message: response.data.error || '发放失败'
        }
      }
    } catch (error: any) {
      console.error('发放奖品失败:', error)
      return {
        success: false,
        message: error.response?.data?.error || '发放失败'
      }
    }
  }

  // 获取抽奖期次列表
  const fetchLotteryPeriods = async () => {
    isLoading.value = true
    try {
      const response = await adminAPI.getLotteryPeriods()
      if (response.data.success) {
        const periods = response.data.data?.periods
        lotteryPeriods.value = Array.isArray(periods) ? periods : []
        return { success: true }
      } else {
        throw new Error(response.data.error || '获取抽奖期次失败')
      }
    } catch (error: any) {
      console.error('获取抽奖期次失败:', error)
      return {
        success: false,
        message: error.response?.data?.error || error.message || '获取抽奖期次失败'
      }
    } finally {
      isLoading.value = false
    }
  }

  // 更新用户状态
  const updateUserStatus = async (userId: number, status: string) => {
    try {
      const response = await adminAPI.updateUser(userId, { status })

      if (response.data.success) {
        // 更新本地用户列表
        const userIndex = users.value.findIndex(u => u.id === userId)
        if (userIndex !== -1) {
          users.value[userIndex].status = status
        }

        return { success: true, message: response.data.message }
      } else {
        return {
          success: false,
          message: response.data.error || '更新用户状态失败'
        }
      }
    } catch (error: any) {
      console.error('更新用户状态失败:', error)
      return {
        success: false,
        message: error.response?.data?.error || '更新用户状态失败'
      }
    }
  }

  // 创建新的抽奖期次
  const createLotteryPeriod = async (periodData: any) => {
    try {
      const response = await adminAPI.createLotteryPeriod(periodData)

      if (response.data.success) {
        // 重新获取期次列表
        await fetchLotteryPeriods()
        return { success: true, message: response.data.message || '抽奖期次创建成功' }
      } else {
        throw new Error(response.data.error || '创建抽奖期次失败')
      }
    } catch (error: any) {
      console.error('创建抽奖期次失败:', error)
      return {
        success: false,
        message: error.response?.data?.error || error.message || '创建抽奖期次失败'
      }
    }
  }

  // 更新抽奖期次状态
  const updatePeriodStatus = async (periodId: number, status: string) => {
    try {
      const response = await adminAPI.updatePeriodStatus(periodId, status)

      if (response.data.success) {
        // 更新本地期次列表
        const periodIndex = lotteryPeriods.value.findIndex(p => p.id === periodId)
        if (periodIndex !== -1) {
          lotteryPeriods.value[periodIndex].status = status
        }
        return { success: true, message: response.data.message || '期次状态更新成功' }
      } else {
        throw new Error(response.data.error || '更新期次状态失败')
      }
    } catch (error: any) {
      console.error('更新期次状态失败:', error)
      return {
        success: false,
        message: error.response?.data?.error || error.message || '更新期次状态失败'
      }
    }
  }

  // 获取管理员列表
  const fetchAdmins = async (params?: { page?: number; limit?: number }) => {
    try {
      isLoading.value = true
      const response = await adminAPI.getAdmins(params)

      if (response.data.success) {
        admins.value = response.data.data.admins
      } else {
        throw new Error(response.data.error || '获取管理员列表失败')
      }
    } catch (error) {
      console.error('获取管理员列表失败:', error)
      throw error
    } finally {
      isLoading.value = false
    }
  }

  // 创建管理员
  const createAdmin = async (adminData: { username: string; password: string; email?: string; role?: string }) => {
    try {
      const response = await adminAPI.createAdmin(adminData)

      if (response.data.success) {
        // 重新获取管理员列表
        await fetchAdmins()
        return response.data
      } else {
        throw new Error(response.data.error || '创建管理员失败')
      }
    } catch (error) {
      console.error('创建管理员失败:', error)
      throw error
    }
  }

  // 更新管理员状态
  const updateAdminStatus = async (adminId: number, status: string) => {
    try {
      const response = await adminAPI.updateAdmin(adminId, { status })

      if (response.data.success) {
        // 更新本地管理员列表
        const adminIndex = admins.value.findIndex(admin => admin.id === adminId)
        if (adminIndex > -1) {
          admins.value[adminIndex].status = status
        }
        return response.data
      } else {
        throw new Error(response.data.error || '更新管理员状态失败')
      }
    } catch (error) {
      console.error('更新管理员状态失败:', error)
      throw error
    }
  }

  // 更新管理员完整信息
  const updateAdminInfo = async (adminId: number, adminData: {
    username?: string;
    email?: string;
    role?: string;
    status?: string;
    password?: string
  }) => {
    try {
      const response = await adminAPI.updateAdmin(adminId, adminData)

      if (response.data.success) {
        // 更新本地管理员列表
        const adminIndex = admins.value.findIndex(admin => admin.id === adminId)
        if (adminIndex > -1) {
          const updatedAdmin = response.data.data.admin
          admins.value[adminIndex] = {
            ...admins.value[adminIndex],
            username: updatedAdmin.username,
            email: updatedAdmin.email,
            role: updatedAdmin.role,
            status: updatedAdmin.status,
            updatedAt: updatedAdmin.updatedAt
          }
        }
        return response.data
      } else {
        throw new Error(response.data.error || '更新管理员信息失败')
      }
    } catch (error) {
      console.error('更新管理员信息失败:', error)
      throw error
    }
  }

  // 删除管理员
  const deleteAdmin = async (adminId: number) => {
    try {
      const response = await adminAPI.deleteAdmin(adminId)

      if (response.data.success) {
        // 从本地管理员列表中移除
        const adminIndex = admins.value.findIndex(admin => admin.id === adminId)
        if (adminIndex > -1) {
          admins.value.splice(adminIndex, 1)
        }
        return response.data
      } else {
        throw new Error(response.data.error || '删除管理员失败')
      }
    } catch (error) {
      console.error('删除管理员失败:', error)
      throw error
    }
  }

  // 获取系统设置
  const fetchSettings = async () => {
    isLoading.value = true
    try {
      const response = await adminAPI.getSettings()
      if (response.data.success) {
        systemSettings.value = response.data.data.systemSettings
        prizeConfig.value = response.data.data.prizeConfig || []
        banners.value = response.data.data.banners || []
        return { success: true }
      } else {
        throw new Error(response.data.error || '获取系统设置失败')
      }
    } catch (error: any) {
      console.error('获取系统设置失败:', error)
      return {
        success: false,
        message: error.response?.data?.error || error.message || '获取系统设置失败'
      }
    } finally {
      isLoading.value = false
    }
  }

  // 更新系统设置
  const updateSettings = async (settingsData: { systemSettings?: any; prizeConfig?: any[]; banners?: any[] }) => {
    try {
      const response = await adminAPI.updateSettings(settingsData)
      if (response.data.success) {
        // 更新本地数据
        if (settingsData.systemSettings) {
          systemSettings.value = { ...systemSettings.value, ...settingsData.systemSettings }
        }
        if (settingsData.prizeConfig) {
          prizeConfig.value = settingsData.prizeConfig
        }
        if (settingsData.banners) {
          banners.value = settingsData.banners
        }
        return { success: true, message: response.data.message || '系统设置更新成功' }
      } else {
        throw new Error(response.data.error || '更新系统设置失败')
      }
    } catch (error: any) {
      console.error('更新系统设置失败:', error)
      return {
        success: false,
        message: error.response?.data?.error || error.message || '更新系统设置失败'
      }
    }
  }

  return {
    // 状态
    stats,
    users,
    lotteryRecords,
    lotteryPeriods,
    admins,
    systemSettings,
    prizeConfig,
    banners,
    isLoading,

    // 方法
    fetchStats,
    fetchRecentActivities,
    fetchUsers,
    fetchLotteryRecords,
    distributePrize,
    fetchLotteryPeriods,
    updateUserStatus,
    createLotteryPeriod,
    updatePeriodStatus,
    fetchSettings,
    updateSettings,
    fetchAdmins,
    createAdmin,
    updateAdminStatus,
    updateAdminInfo,
    deleteAdmin
  }
})
