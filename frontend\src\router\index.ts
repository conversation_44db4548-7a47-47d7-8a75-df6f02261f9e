import { createRouter, createWebHistory } from 'vue-router'
import { useAuthStore } from '../stores/auth'
import HomeView from '../views/HomeView.vue'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      name: 'home',
      component: HomeView,
    },
    {
      path: '/login',
      name: 'login',
      component: () => import('../views/LoginView.vue'),
    },
    {
      path: '/register',
      name: 'register',
      component: () => import('../views/RegisterView.vue'),
    },
    {
      path: '/lottery',
      name: 'lottery',
      component: () => import('../views/LotteryView.vue'),
      meta: { requiresAuth: true }
    },
    {
      path: '/records',
      name: 'records',
      component: () => import('../views/RecordsView.vue'),
      meta: { requiresAuth: true }
    },
    {
      path: '/rules',
      name: 'rules',
      component: () => import('../views/RulesView.vue')
    },
    {
      path: '/admin/login',
      name: 'admin-login',
      component: () => import('../views/admin/AdminLogin.vue')
    },
    {
      path: '/admin',
      component: () => import('../views/admin/AdminLayout.vue'),
      meta: { requiresAuth: true, requiresAdmin: true },
      children: [
        {
          path: '',
          redirect: '/admin/dashboard'
        },
        {
          path: 'dashboard',
          name: 'admin-dashboard',
          component: () => import('../views/admin/AdminDashboard.vue'),
        },
        {
          path: 'users',
          name: 'admin-users',
          component: () => import('../views/admin/AdminUsers.vue'),
        },
        {
          path: 'admins',
          name: 'admin-managers',
          component: () => import('../views/admin/AdminManagers.vue'),
        },
        {
          path: 'lottery-records',
          name: 'admin-lottery-records',
          component: () => import('../views/admin/AdminLotteryRecords.vue'),
        },
        {
          path: 'lottery-periods',
          name: 'admin-lottery-periods',
          component: () => import('../views/admin/AdminLotteryPeriods.vue'),
        },
        {
          path: 'user-logs',
          name: 'admin-user-logs',
          component: () => import('../views/admin/AdminUserLogs.vue'),
        },
        {
          path: 'admin-logs',
          name: 'admin-logs',
          component: () => import('../views/admin/AdminLogs.vue'),
        },
        {
          path: 'settings',
          name: 'admin-settings',
          component: () => import('../views/admin/AdminSettings.vue'),
        },
      ]
    }
  ],
})

// 路由守卫
router.beforeEach((to, _from, next) => {
  const authStore = useAuthStore()

  // 如果已经是管理员且访问管理员登录页，重定向到管理后台
  if (to.path === '/admin/login' && authStore.isAdminAuthenticated) {
    next('/admin')
    return
  }

  // 检查是否需要管理员权限
  if (to.meta.requiresAdmin && !authStore.isAdminAuthenticated) {
    // 如果没有管理员认证，跳转到管理员登录页
    next('/admin/login')
    return
  }

  // 检查是否需要普通用户认证（非管理员路由）
  if (to.meta.requiresAuth && !to.meta.requiresAdmin && !authStore.isAuthenticated) {
    next('/login')
    return
  }

  next()
})

export default router
