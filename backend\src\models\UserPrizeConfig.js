const { DataTypes } = require('sequelize')
const { sequelize } = require('../config/database')

const UserPrizeConfig = sequelize.define('UserPrizeConfig', {
  id: {
    type: DataTypes.BIGINT,
    primaryKey: true,
    autoIncrement: true,
    comment: '主键ID'
  },
  userId: {
    type: DataTypes.BIGINT,
    allowNull: false,
    field: 'user_id',
    comment: '用户ID'
  },
  prizeConfigs: {
    type: DataTypes.JSON,
    allowNull: false,
    field: 'prize_configs',
    comment: '奖品配置JSON数据'
  },
  totalProbability: {
    type: DataTypes.DECIMAL(5, 4),
    allowNull: false,
    defaultValue: 1.0000,
    field: 'total_probability',
    comment: '总概率(应该等于1.0000)'
  },
  isEnabled: {
    type: DataTypes.BOOLEAN,
    allowNull: false,
    defaultValue: false,
    field: 'is_enabled',
    comment: '是否启用个人设置(0:使用全局设置, 1:使用个人设置)'
  },
  createdAt: {
    type: DataTypes.DATE,
    allowNull: false,
    defaultValue: DataTypes.NOW,
    field: 'created_at',
    comment: '创建时间'
  },
  updatedAt: {
    type: DataTypes.DATE,
    allowNull: false,
    defaultValue: DataTypes.NOW,
    field: 'updated_at',
    comment: '更新时间'
  }
}, {
  tableName: 'user_prize_configs',
  timestamps: true,
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  indexes: [
    {
      unique: true,
      fields: ['user_id'],
      name: 'uk_user_id'
    },
    {
      fields: ['is_enabled'],
      name: 'idx_enabled'
    },
    {
      fields: ['total_probability'],
      name: 'idx_total_probability'
    }
  ],
  comment: '用户个人抽奖率配置表(JSON格式)'
})

module.exports = UserPrizeConfig
