<template>
  <div class="min-h-screen bg-black text-white">
    <!-- 使用公共头部组件 -->
    <AppHeader />

    <!-- 主要内容区域 -->
    <main class="container mx-auto px-4 py-6">
      <!-- 页面标题 -->
      <section class="mb-8">
        <div class="text-center">
          <h1 class="text-3xl md:text-4xl font-bold text-gold-400 mb-4">
            <span class="mr-3">📋</span>活动规则
          </h1>
          <p class="text-gray-400 text-lg">了解抽奖活动的详细规则和说明</p>
        </div>
      </section>

      <!-- 规则导航 -->
      <section class="mb-8">
        <div class="flex flex-wrap justify-center gap-2 md:gap-4">
          <button 
            v-for="tab in ruleTabs" 
            :key="tab.id"
            @click="activeTab = tab.id"
            :class="[
              'px-4 py-2 rounded-lg font-medium transition-all duration-200',
              activeTab === tab.id 
                ? 'bg-gold-600 text-black' 
                : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
            ]"
          >
            <span class="mr-2">{{ tab.icon }}</span>{{ tab.name }}
          </button>
        </div>
      </section>

      <!-- 规则内容 -->
      <section class="mb-8">
        <!-- 基本规则 -->
        <div v-if="activeTab === 'basic'" class="space-y-6">
          <div class="bg-gradient-to-br from-gray-800 to-gray-900 rounded-xl p-6 border border-gold-500/30">
            <h2 class="text-2xl font-bold text-gold-400 mb-4 flex items-center">
              <span class="mr-3">🎯</span>基本规则
            </h2>
            <div class="space-y-4 text-gray-300">
              <div class="flex items-start space-x-3">
                <span class="text-gold-400 font-bold">1.</span>
                <div>
                  <h3 class="font-semibold text-white mb-2">参与条件</h3>
                  <p>用户必须完成注册并登录账户后方可参与抽奖活动。每位用户每日可免费参与3次抽奖。</p>
                </div>
              </div>
              
              <div class="flex items-start space-x-3">
                <span class="text-gold-400 font-bold">2.</span>
                <div>
                  <h3 class="font-semibold text-white mb-2">抽奖时间</h3>
                  <p>抽奖活动全天24小时开放，每日凌晨00:00重置抽奖次数。</p>
                </div>
              </div>
              
              <div class="flex items-start space-x-3">
                <span class="text-gold-400 font-bold">3.</span>
                <div>
                  <h3 class="font-semibold text-white mb-2">抽奖方式</h3>
                  <p>采用随机算法确保公平性，所有奖品均有真实中奖概率，系统自动判定中奖结果。</p>
                </div>
              </div>
              
              <div class="flex items-start space-x-3">
                <span class="text-gold-400 font-bold">4.</span>
                <div>
                  <h3 class="font-semibold text-white mb-2">结果公示</h3>
                  <p>中奖结果实时显示，所有中奖记录可在"中奖记录"页面查询，确保透明公正。</p>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 奖品说明 -->
        <div v-if="activeTab === 'prizes'" class="space-y-6">
          <div class="bg-gradient-to-br from-gray-800 to-gray-900 rounded-xl p-6 border border-gold-500/30">
            <h2 class="text-2xl font-bold text-gold-400 mb-4 flex items-center">
              <span class="mr-3">🏆</span>奖品设置
            </h2>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div v-for="prize in prizeList" :key="prize.id" class="bg-gray-700/50 rounded-lg p-4 border border-gray-600">
                <div class="flex items-center justify-between mb-2">
                  <h3 class="font-bold text-white">{{ prize.name }}</h3>
                  <span class="text-gold-400 font-bold">{{ prize.value }}</span>
                </div>
                <div class="text-sm text-gray-400 mb-2">中奖概率: {{ prize.probability }}%</div>
                <p class="text-sm text-gray-300">{{ prize.description }}</p>
              </div>
            </div>
          </div>
        </div>

        <!-- 中奖概率 -->
        <div v-if="activeTab === 'probability'" class="space-y-6">
          <div class="bg-gradient-to-br from-gray-800 to-gray-900 rounded-xl p-6 border border-gold-500/30">
            <h2 class="text-2xl font-bold text-gold-400 mb-4 flex items-center">
              <span class="mr-3">📊</span>中奖概率
            </h2>
            
            <div class="space-y-4">
              <div class="bg-blue-600/20 border border-blue-500/30 rounded-lg p-4">
                <h3 class="font-bold text-blue-400 mb-2">概率说明</h3>
                <p class="text-gray-300 text-sm">
                  本平台采用真实随机算法，所有奖品的中奖概率均为真实设定，不存在虚假概率。
                  每次抽奖都是独立事件，不受之前抽奖结果影响。
                </p>
              </div>
              
              <div class="space-y-3">
                <div v-for="prize in prizeList" :key="prize.id" class="flex items-center justify-between p-3 bg-gray-700/30 rounded-lg">
                  <span class="text-white font-medium">{{ prize.name }}</span>
                  <div class="flex items-center space-x-4">
                    <span class="text-gold-400">{{ prize.probability }}%</span>
                    <div class="w-32 bg-gray-600 rounded-full h-2">
                      <div 
                        class="bg-gradient-to-r from-gold-400 to-gold-600 h-2 rounded-full"
                        :style="{ width: prize.probability + '%' }"
                      ></div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 注意事项 -->
        <div v-if="activeTab === 'notice'" class="space-y-6">
          <div class="bg-gradient-to-br from-gray-800 to-gray-900 rounded-xl p-6 border border-gold-500/30">
            <h2 class="text-2xl font-bold text-gold-400 mb-4 flex items-center">
              <span class="mr-3">⚠️</span>注意事项
            </h2>
            
            <div class="space-y-4">
              <div class="bg-red-600/20 border border-red-500/30 rounded-lg p-4">
                <h3 class="font-bold text-red-400 mb-2">重要提醒</h3>
                <ul class="space-y-2 text-gray-300 text-sm">
                  <li class="flex items-start space-x-2">
                    <span class="text-red-400 mt-1">•</span>
                    <span>请确保账户信息真实有效，虚假信息将影响奖品发放</span>
                  </li>
                  <li class="flex items-start space-x-2">
                    <span class="text-red-400 mt-1">•</span>
                    <span>中奖后请及时联系客服领取奖品，逾期视为自动放弃</span>
                  </li>
                  <li class="flex items-start space-x-2">
                    <span class="text-red-400 mt-1">•</span>
                    <span>禁止使用外挂、脚本等技术手段参与抽奖</span>
                  </li>
                </ul>
              </div>
              
              <div class="bg-yellow-600/20 border border-yellow-500/30 rounded-lg p-4">
                <h3 class="font-bold text-yellow-400 mb-2">领奖须知</h3>
                <ul class="space-y-2 text-gray-300 text-sm">
                  <li class="flex items-start space-x-2">
                    <span class="text-yellow-400 mt-1">•</span>
                    <span>现金奖品将在3个工作日内发放到账户余额</span>
                  </li>
                  <li class="flex items-start space-x-2">
                    <span class="text-yellow-400 mt-1">•</span>
                    <span>实物奖品需提供收货地址，7个工作日内发货</span>
                  </li>
                  <li class="flex items-start space-x-2">
                    <span class="text-yellow-400 mt-1">•</span>
                    <span>如有疑问请及时联系在线客服</span>
                  </li>
                </ul>
              </div>
              
              <div class="bg-green-600/20 border border-green-500/30 rounded-lg p-4">
                <h3 class="font-bold text-green-400 mb-2">平台承诺</h3>
                <ul class="space-y-2 text-gray-300 text-sm">
                  <li class="flex items-start space-x-2">
                    <span class="text-green-400 mt-1">•</span>
                    <span>所有抽奖活动均为真实有效，绝无虚假</span>
                  </li>
                  <li class="flex items-start space-x-2">
                    <span class="text-green-400 mt-1">•</span>
                    <span>采用先进的随机算法，确保抽奖公平公正</span>
                  </li>
                  <li class="flex items-start space-x-2">
                    <span class="text-green-400 mt-1">•</span>
                    <span>用户隐私和资金安全得到严格保护</span>
                  </li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </section>

      <!-- 联系客服 -->
      <section class="mb-8">
        <div class="bg-gradient-to-r from-blue-600 to-blue-700 rounded-xl p-6 text-center">
          <h3 class="text-xl font-bold text-white mb-2">还有疑问？</h3>
          <p class="text-blue-100 mb-4">我们的客服团队24小时为您服务</p>
          <div class="flex flex-col md:flex-row gap-4 justify-center">
            <button class="px-6 py-3 bg-white text-blue-600 font-bold rounded-lg hover:bg-gray-100 transition-colors">
              <span class="mr-2">💬</span>在线客服
            </button>
            <button class="px-6 py-3 bg-blue-800 text-white font-bold rounded-lg hover:bg-blue-900 transition-colors">
              <span class="mr-2">📞</span>客服热线: 400-888-8888
            </button>
          </div>
        </div>
      </section>
    </main>

    <!-- 使用公共页脚组件 -->
    <AppFooter />
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import AppHeader from '@/components/AppHeader.vue'
import AppFooter from '@/components/AppFooter.vue'

// 当前激活的标签页
const activeTab = ref('basic')

// 规则标签页
const ruleTabs = [
  { id: 'basic', name: '基本规则', icon: '🎯' },
  { id: 'prizes', name: '奖品说明', icon: '🏆' },
  { id: 'probability', name: '中奖概率', icon: '📊' },
  { id: 'notice', name: '注意事项', icon: '⚠️' }
]

// 奖品列表
const prizeList = [
  {
    id: 1,
    name: '特等奖',
    value: '¥5,000',
    probability: 0.1,
    description: '现金大奖，直接发放到账户余额'
  },
  {
    id: 2,
    name: '一等奖',
    value: '¥1,000',
    probability: 0.5,
    description: '现金奖励，3个工作日内到账'
  },
  {
    id: 3,
    name: '二等奖',
    value: '¥500',
    probability: 1.0,
    description: '现金奖励，3个工作日内到账'
  },
  {
    id: 4,
    name: '三等奖',
    value: '¥100',
    probability: 3.0,
    description: '现金奖励，3个工作日内到账'
  },
  {
    id: 5,
    name: '四等奖',
    value: '¥50',
    probability: 5.0,
    description: '现金奖励，3个工作日内到账'
  },
  {
    id: 6,
    name: '五等奖',
    value: '¥10',
    probability: 10.0,
    description: '现金奖励，3个工作日内到账'
  },
  {
    id: 7,
    name: '参与奖',
    value: '¥5',
    probability: 20.0,
    description: '感谢参与，小额现金奖励'
  },
  {
    id: 8,
    name: '谢谢参与',
    value: '再接再厉',
    probability: 60.4,
    description: '很遗憾未中奖，请继续努力'
  }
]
</script>

<style scoped>
/* 自定义样式 */
</style>
