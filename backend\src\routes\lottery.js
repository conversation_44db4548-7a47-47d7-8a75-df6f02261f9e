const express = require('express')
const { Op, Transaction } = require('sequelize')
const { sequelize } = require('../config/database')
const { authenticateToken } = require('../middleware/auth')
const {
  User,
  LotteryPeriod,
  PrizeConfig,
  LotteryRecord,
  UserLotteryLimit,
  UserPrizeConfig
} = require('../models')

const router = express.Router()

// 添加路由调试中间件
router.use((req, res, next) => {
  console.log(`🎯 抽奖路由请求: ${req.method} ${req.path}`)
  next()
})

/**
 * 获取当前活动期次
 * GET /api/lottery/current-period
 */
router.get('/current-period', async (req, res) => {
  try {
    // 获取当前期次
    const currentPeriod = await LotteryPeriod.findOne({
      where: {
        status: {
          [Op.in]: ['进行中', '未开始']
        }
      },
      order: [['periodNumber', 'DESC']]
    })

    if (!currentPeriod) {
      return res.status(404).json({
        success: false,
        error: '当前没有活动期次'
      })
    }

    // 获取通用奖品配置（忽略period_id，使用所有激活的奖品配置）
    const prizes = await PrizeConfig.findAll({
      where: { isActive: true },
      order: [['sortOrder', 'ASC']]
    })

    res.json({
      success: true,
      data: {
        period: {
          id: currentPeriod.id,
          periodName: currentPeriod.periodName,
          periodNumber: currentPeriod.periodNumber,
          startTime: currentPeriod.startTime,
          endTime: currentPeriod.endTime,
          drawTime: currentPeriod.drawTime,
          status: currentPeriod.status,
          maxAttemptsPerUser: currentPeriod.maxAttemptsPerUser,
          totalParticipants: currentPeriod.totalParticipants,
          totalAttempts: currentPeriod.totalAttempts,
          totalPrizeAmount: parseFloat(currentPeriod.totalPrizeAmount) || 0,
          actualDrawableAmount: parseFloat(currentPeriod.actualDrawableAmount) || 0,
          totalShares: currentPeriod.totalShares || 0,
          prizes: prizes || []
        }
      }
    })
  } catch (error) {
    console.error('获取当前期次错误:', error)
    res.status(500).json({
      success: false,
      error: '获取期次信息失败'
    })
  }
})

/**
 * 获取用户抽奖限制信息
 * GET /api/lottery/user-limit
 */
router.get('/user-limit', authenticateToken, async (req, res) => {
  try {
    const { periodId } = req.query
    const userId = req.user.userId

    if (!periodId) {
      return res.status(400).json({
        success: false,
        error: '请提供期次ID'
      })
    }

    // 查找或创建用户抽奖限制记录
    let userLimit = await UserLotteryLimit.findOne({
      where: {
        userId,
        periodId: parseInt(periodId)
      }
    })

    if (!userLimit) {
      // 获取期次的默认抽奖次数
      const period = await LotteryPeriod.findByPk(periodId)
      if (!period) {
        return res.status(404).json({
          success: false,
          error: '期次不存在'
        })
      }

      // 创建用户抽奖限制记录
      const user = await User.findByPk(userId)
      userLimit = await UserLotteryLimit.create({
        userId,
        periodId: parseInt(periodId),
        platformId: user.platformId,
        maxAttempts: period.maxAttemptsPerUser,
        usedAttempts: 0
      })
    }

    res.json({
      success: true,
      data: {
        limit: {
          periodId: userLimit.periodId,
          maxAttempts: userLimit.maxAttempts,
          usedAttempts: userLimit.usedAttempts,
          remainingAttempts: userLimit.maxAttempts - userLimit.usedAttempts,
          lastAttemptAt: userLimit.lastAttemptAt
        }
      }
    })
  } catch (error) {
    console.error('获取用户抽奖限制错误:', error)
    res.status(500).json({
      success: false,
      error: '获取抽奖限制失败'
    })
  }
})

/**
 * 执行抽奖 - 支持个人中奖率配置
 * POST /api/lottery/draw
 */
router.post('/draw', authenticateToken, async (req, res) => {
  const transaction = await sequelize.transaction({
    isolationLevel: Transaction.ISOLATION_LEVELS.READ_COMMITTED
  })

  try {
    const { periodId } = req.body
    const userId = req.user.userId

    if (!periodId) {
      await transaction.rollback()
      return res.status(400).json({
        success: false,
        error: '请提供期次ID'
      })
    }

    // 1. 验证期次存在性和状态（加行级锁）
    const period = await LotteryPeriod.findByPk(periodId, {
      transaction,
      lock: true
    })

    if (!period) {
      await transaction.rollback()
      return res.status(404).json({
        success: false,
        error: '期次不存在'
      })
    }

    if (period.status !== '进行中') {
      await transaction.rollback()
      return res.status(400).json({
        success: false,
        error: '当前期次不可用或已结束'
      })
    }

    // 2. 验证时间有效性
    const timeValidation = validateLotteryTime(period)
    if (!timeValidation.valid) {
      await transaction.rollback()
      return res.status(400).json({
        success: false,
        error: timeValidation.message
      })
    }

    // 3. 验证用户抽奖次数
    const attemptsValidation = await validateUserAttempts(
      userId, periodId, period.maxAttemptsPerUser, transaction
    )

    if (!attemptsValidation.valid) {
      await transaction.rollback()
      return res.status(400).json({
        success: false,
        error: attemptsValidation.message,
        data: {
          remainingAttempts: attemptsValidation.remainingAttempts,
          usedAttempts: attemptsValidation.usedAttempts
        }
      })
    }

    // 4. 获取有效的抽奖概率配置
    const prizeConfigResult = await getUserEffectivePrizeConfigs(
      userId, periodId, transaction
    )

    if (!prizeConfigResult.configs.length) {
      await transaction.rollback()
      return res.status(400).json({
        success: false,
        error: '当前期次没有可用奖品配置'
      })
    }

    // 5. 执行个人化抽奖算法
    const drawResult = performPersonalizedLotteryDraw(
      prizeConfigResult.configs, userId
    )
    
    // 6. 创建抽奖记录
    const user = await User.findByPk(userId, { transaction })
    const record = await LotteryRecord.create({
      periodId,
      userId,
      platformId: user.platformId,
      prizeName: drawResult.prizeName,
      prizeAmount: drawResult.prizeAmount,
      isWinner: drawResult.isWinner,
      drawResult: {
        configSource: prizeConfigResult.source,
        usedConfigs: prizeConfigResult.configs,
        winningPrize: drawResult,
        drawTime: new Date()
      },
      status: drawResult.isWinner ? '待发放' : '已发放'
    }, { transaction })

    // 7. 更新期次统计
    // 检查用户是否首次参与该期次
    const userFirstTimeParticipation = attemptsValidation.usedAttempts === 0 // 使用验证结果中的已使用次数

    const updateData = {
      totalAttempts: period.totalAttempts + 1
    }

    // 如果是首次参与，增加参与人数
    if (userFirstTimeParticipation) {
      updateData.totalParticipants = period.totalParticipants + 1
      console.log(`👥 新用户参与，参与人数 +1: ${period.totalParticipants} -> ${updateData.totalParticipants}`)
    }

    await period.update(updateData, { transaction })

    // 8. 如果中奖，更新用户总中奖金额
    if (drawResult.isWinner && drawResult.prizeAmount > 0) {
      // 更新用户总中奖金额
      await user.update({
        totalWinnings: parseFloat(user.totalWinnings) + parseFloat(drawResult.prizeAmount)
      }, { transaction })
    }

    await transaction.commit()

    // 9. 返回抽奖结果
    res.json({
      success: true,
      message: drawResult.isWinner ? '恭喜中奖！' : '谢谢参与！',
      data: {
        record: {
          id: record.id,
          prizeName: record.prizeName,
          prizeAmount: parseFloat(record.prizeAmount),
          isWinner: record.isWinner,
          drawnAt: record.drawnAt
        },
        remainingAttempts: attemptsValidation.remainingAttempts - 1,
        configSource: prizeConfigResult.source
      }
    })
  } catch (error) {
    await transaction.rollback()
    console.error('抽奖执行错误:', error)
    res.status(500).json({
      success: false,
      error: '抽奖失败，请稍后重试'
    })
  }
})

/**
 * 抽奖算法
 * @param {Array} prizes 奖品配置数组
 * @returns {Object} 抽奖结果
 */
function performLotteryDraw(prizes) {
  // 计算总概率
  const totalProbability = prizes.reduce((sum, prize) => sum + parseFloat(prize.probability), 0)
  
  // 生成随机数
  const random = Math.random() * totalProbability
  
  // 确定中奖奖品
  let currentProbability = 0
  for (const prize of prizes) {
    currentProbability += parseFloat(prize.probability)
    if (random <= currentProbability) {
      return {
        prizeLevel: prize.prizeLevel,
        prizeName: prize.prizeName,
        prizeAmount: parseFloat(prize.prizeAmount),
        isWinner: prize.prizeAmount > 0,
        probability: prize.probability
      }
    }
  }
  
  // 兜底返回最后一个奖品（通常是谢谢参与）
  const lastPrize = prizes[prizes.length - 1]
  return {
    prizeLevel: lastPrize.prizeLevel,
    prizeName: lastPrize.prizeName,
    prizeAmount: parseFloat(lastPrize.prizeAmount),
    isWinner: lastPrize.prizeAmount > 0,
    probability: lastPrize.probability
  }
}

/**
 * 获取用户抽奖记录
 * GET /api/lottery/records
 */
router.get('/records', authenticateToken, async (req, res) => {
  try {
    const { page = 1, limit = 10, periodId } = req.query
    const userId = req.user.userId
    const offset = (parseInt(page) - 1) * parseInt(limit)

    // 构建查询条件
    const whereCondition = { userId }
    if (periodId) {
      whereCondition.periodId = parseInt(periodId)
    }

    // 查询记录
    const { count, rows: records } = await LotteryRecord.findAndCountAll({
      where: whereCondition,
      include: [{
        model: LotteryPeriod,
        as: 'period',
        attributes: ['id', 'periodName', 'periodNumber']
      }],
      order: [['drawnAt', 'DESC']],
      limit: parseInt(limit),
      offset
    })

    // 格式化记录数据
    const formattedRecords = records.map(record => ({
      id: record.id,
      periodId: record.periodId,
      periodName: record.period?.periodName || '未知期次',
      prizeLevel: record.prizeLevel,
      prizeName: record.prizeName,
      prizeAmount: parseFloat(record.prizeAmount),
      isWinner: record.isWinner,
      status: record.status,
      drawnAt: record.drawnAt
    }))

    res.json({
      success: true,
      data: {
        records: formattedRecords,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total: count,
          totalPages: Math.ceil(count / parseInt(limit))
        }
      }
    })
  } catch (error) {
    console.error('获取抽奖记录错误:', error)
    res.status(500).json({
      success: false,
      error: '获取抽奖记录失败'
    })
  }
})

/**
 * 获取用户状态信息（包括剩余抽奖次数等）
 * GET /api/lottery/user-status
 */
router.get('/user-status', authenticateToken, async (req, res) => {
  console.log('🔍 收到用户状态请求:', {
    url: req.url,
    method: req.method,
    headers: req.headers.authorization ? '有Token' : '无Token',
    userObject: req.user,
    userId: req.user?.userId,
    userKeys: req.user ? Object.keys(req.user) : 'req.user is undefined'
  })

  try {
    const userId = req.user.userId

    // 获取用户信息
    const user = await User.findByPk(userId)
    if (!user) {
      return res.status(404).json({
        success: false,
        error: '用户不存在'
      })
    }

    // 获取当前活动期次
    console.log('🔍 查询当前活动期次...')
    const currentPeriod = await LotteryPeriod.findOne({
      where: {
        status: '进行中'
      },
      order: [['periodNumber', 'DESC']]
    })
    console.log('📊 当前期次查询结果:', currentPeriod ? {
      id: currentPeriod.id,
      periodName: currentPeriod.periodName,
      status: currentPeriod.status
    } : 'null')

    // 如果没找到进行中的期次，尝试查找所有期次进行调试
    if (!currentPeriod) {
      console.log('⚠️ 没有找到进行中的期次，查询所有期次进行调试...')
      const allPeriods = await LotteryPeriod.findAll({
        order: [['periodNumber', 'DESC']]
      })
      console.log('📋 所有期次:', allPeriods.map(p => ({
        id: p.id,
        periodName: p.periodName,
        status: p.status
      })))
    }

    let userStatus = {
      user: {
        id: user.id,
        platformId: user.platformId,
        username: user.username,
        status: user.status,
        totalWinnings: parseFloat(user.totalWinnings)
      },
      currentPeriod: null,
      remainingAttempts: 0,
      totalRecords: 0,
      winRecords: 0
    }

    if (currentPeriod) {
      // 获取用户在当前期次的实际抽奖次数（从抽奖记录表查询）
      const usedAttempts = await LotteryRecord.count({
        where: {
          userId,
          periodId: currentPeriod.id
        }
      })

      // 获取或创建用户抽奖限制记录（主要用于记录最大次数）
      let userLimit = await UserLotteryLimit.findOne({
        where: {
          userId,
          periodId: currentPeriod.id
        }
      })

      if (!userLimit) {
        userLimit = await UserLotteryLimit.create({
          userId,
          periodId: currentPeriod.id,
          platformId: user.platformId,
          maxAttempts: currentPeriod.maxAttemptsPerUser,
          usedAttempts: 0
        })
      }

      userStatus.currentPeriod = {
        id: currentPeriod.id,
        periodName: currentPeriod.periodName,
        periodNumber: currentPeriod.periodNumber,
        endTime: currentPeriod.endTime
      }
      userStatus.remainingAttempts = userLimit.maxAttempts - usedAttempts
    }

    // 获取用户总抽奖记录数和中奖记录数
    const [totalRecords, winRecords] = await Promise.all([
      LotteryRecord.count({ where: { userId } }),
      LotteryRecord.count({ where: { userId, isWinner: true } })
    ])

    userStatus.totalRecords = totalRecords
    userStatus.winRecords = winRecords

    res.json({
      success: true,
      data: userStatus
    })
  } catch (error) {
    console.error('获取用户状态错误:', error)
    res.status(500).json({
      success: false,
      error: '获取用户状态失败'
    })
  }
})

/**
 * 验证抽奖时间有效性
 * @param {Object} period - 期次对象
 * @returns {Object} 验证结果
 */
function validateLotteryTime(period) {
  const now = new Date()
  const startTime = new Date(period.startTime)
  const endTime = new Date(period.endTime)

  if (now < startTime) {
    return { valid: false, message: '抽奖活动尚未开始' }
  }

  if (now > endTime) {
    return { valid: false, message: '抽奖活动已结束' }
  }

  return { valid: true }
}

/**
 * 验证用户抽奖次数
 * @param {number} userId - 用户ID
 * @param {number} periodId - 期次ID
 * @param {number} maxAttempts - 最大抽奖次数
 * @param {Object} transaction - 数据库事务
 * @returns {Object} 验证结果和剩余次数
 */
async function validateUserAttempts(userId, periodId, maxAttempts, transaction) {
  // 从抽奖记录表统计用户已使用次数
  const usedAttempts = await LotteryRecord.count({
    where: { userId, periodId },
    transaction
  })

  const remainingAttempts = maxAttempts - usedAttempts

  if (remainingAttempts <= 0) {
    return {
      valid: false,
      message: '本期抽奖次数已用完',
      remainingAttempts: 0,
      usedAttempts
    }
  }

  return {
    valid: true,
    remainingAttempts,
    usedAttempts
  }
}

/**
 * 获取用户有效的抽奖概率配置
 * @param {number} userId - 用户ID
 * @param {number} periodId - 期次ID
 * @param {Object} transaction - 数据库事务
 * @returns {Object} 概率配置和配置来源
 */
async function getUserEffectivePrizeConfigs(userId, periodId, transaction) {
  // 1. 检查用户是否启用个人抽奖率
  const user = await User.findByPk(userId, { transaction })

  if (user.customLotteryEnabled) {
    // 2. 获取用户个人配置
    const userPrizeConfig = await UserPrizeConfig.findOne({
      where: { userId },
      transaction
    })

    if (userPrizeConfig && userPrizeConfig.isEnabled) {
      // 3. 验证个人配置总概率是否为100%
      if (Math.abs(userPrizeConfig.totalProbability - 1.0) < 0.0001) {
        return {
          configs: userPrizeConfig.prizeConfigs.configs,
          source: 'personal',
          totalProbability: userPrizeConfig.totalProbability
        }
      }
    }
  }

  // 4. 使用全局配置
  // 首先尝试获取当前期次的配置
  let globalConfigs = await PrizeConfig.findAll({
    where: { periodId, isActive: true },
    order: [['sortOrder', 'ASC']],
    transaction
  })

  // 如果当前期次没有配置，使用最新的可用配置
  if (globalConfigs.length === 0) {
    console.log(`⚠️ 期次${periodId}没有奖品配置，使用最新可用配置`)
    globalConfigs = await PrizeConfig.findAll({
      where: { isActive: true },
      order: [['periodId', 'DESC'], ['sortOrder', 'ASC']],
      limit: 12, // 假设有12个奖品等级
      transaction
    })
  }

  const globalConfigsFormatted = globalConfigs.map(config => ({
    prizeConfigId: config.id,
    prizeName: config.prizeName,
    prizeAmount: parseFloat(config.prizeAmount),
    customProbability: parseFloat(config.probability),
    isEnabled: true
  }))

  const totalProbability = globalConfigs.reduce((sum, config) =>
    sum + parseFloat(config.probability), 0)

  return {
    configs: globalConfigsFormatted,
    source: 'global',
    totalProbability
  }
}

/**
 * 个人化抽奖算法
 * @param {Array} prizeConfigs - 奖品配置（包含个人或全局概率）
 * @param {number} userId - 用户ID（用于日志）
 * @returns {Object} 抽奖结果
 */
function performPersonalizedLotteryDraw(prizeConfigs, userId) {
  // 计算总概率
  const totalProbability = prizeConfigs.reduce((sum, config) =>
    sum + parseFloat(config.customProbability), 0)

  // 生成随机数
  const random = Math.random() * totalProbability

  console.log(`用户${userId}抽奖 - 随机数: ${random}, 总概率: ${totalProbability}`)

  // 确定中奖奖品
  let currentProbability = 0
  for (const config of prizeConfigs) {
    currentProbability += parseFloat(config.customProbability)
    if (random <= currentProbability) {
      console.log(`用户${userId}中奖 - 奖品: ${config.prizeName}, 概率区间: ${currentProbability}`)
      return {
        prizeName: config.prizeName,
        prizeAmount: parseFloat(config.prizeAmount),
        isWinner: config.prizeAmount > 0,
        probability: config.customProbability,
        configId: config.prizeConfigId
      }
    }
  }

  // 兜底返回最后一个奖品
  const lastConfig = prizeConfigs[prizeConfigs.length - 1]
  console.log(`用户${userId}兜底中奖 - 奖品: ${lastConfig.prizeName}`)
  return {
    prizeName: lastConfig.prizeName,
    prizeAmount: parseFloat(lastConfig.prizeAmount),
    isWinner: lastConfig.prizeAmount > 0,
    probability: lastConfig.customProbability,
    configId: lastConfig.prizeConfigId
  }
}

module.exports = router
