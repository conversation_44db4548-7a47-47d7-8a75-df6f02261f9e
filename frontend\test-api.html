<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API测试页面</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .result { margin: 10px 0; padding: 10px; border: 1px solid #ccc; background: #f9f9f9; }
        .error { background: #ffebee; border-color: #f44336; }
        .success { background: #e8f5e9; border-color: #4caf50; }
        button { margin: 5px; padding: 10px 20px; }
    </style>
</head>
<body>
    <h1>API测试页面</h1>
    
    <button onclick="testCurrentPeriod()">测试当前期次API</button>
    <button onclick="testUserStatus()">测试用户状态API</button>
    <button onclick="testUserLogin()">测试用户登录</button>
    <button onclick="clearStorage()">清理localStorage</button>
    <button onclick="checkStorage()">检查localStorage</button>
    
    <div id="results"></div>

    <script>
        const API_BASE = 'http://localhost:3000/api';
        
        function addResult(title, data, isError = false) {
            const results = document.getElementById('results');
            const div = document.createElement('div');
            div.className = `result ${isError ? 'error' : 'success'}`;
            div.innerHTML = `
                <h3>${title}</h3>
                <pre>${JSON.stringify(data, null, 2)}</pre>
            `;
            results.appendChild(div);
        }
        
        async function testCurrentPeriod() {
            try {
                console.log('🔍 测试当前期次API...');
                const response = await fetch(`${API_BASE}/lottery/current-period`);
                const data = await response.json();
                console.log('📡 API响应:', data);
                addResult('当前期次API测试', data);
            } catch (error) {
                console.error('❌ API调用失败:', error);
                addResult('当前期次API测试失败', error.message, true);
            }
        }
        
        async function testUserStatus() {
            try {
                console.log('🔍 测试用户状态API...');
                const token = localStorage.getItem('token');
                if (!token) {
                    addResult('用户状态API测试', '未找到token，请先登录', true);
                    return;
                }
                
                const response = await fetch(`${API_BASE}/lottery/user-status`, {
                    headers: {
                        'Authorization': `Bearer ${token}`
                    }
                });
                const data = await response.json();
                console.log('📡 API响应:', data);
                addResult('用户状态API测试', data);
            } catch (error) {
                console.error('❌ API调用失败:', error);
                addResult('用户状态API测试失败', error.message, true);
            }
        }
        
        async function testUserLogin() {
            try {
                console.log('🔍 测试用户登录API...');
                const response = await fetch(`${API_BASE}/auth/login`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        username: 'testuser',
                        password: '123456'
                    })
                });
                const data = await response.json();
                console.log('📡 API响应:', data);
                
                if (data.success && data.data.token) {
                    localStorage.setItem('token', data.data.token);
                    addResult('用户登录API测试', '登录成功，token已保存');
                } else {
                    addResult('用户登录API测试', data, true);
                }
            } catch (error) {
                console.error('❌ API调用失败:', error);
                addResult('用户登录API测试失败', error.message, true);
            }
        }
        
        function clearStorage() {
            localStorage.clear();
            addResult('清理localStorage', '已清理所有localStorage数据');
        }

        function checkStorage() {
            const token = localStorage.getItem('token');
            const adminToken = localStorage.getItem('adminToken');
            const data = {
                token: token ? `存在 (${token.substring(0, 20)}...)` : '不存在',
                adminToken: adminToken ? `存在 (${adminToken.substring(0, 20)}...)` : '不存在',
                allKeys: Object.keys(localStorage)
            };
            addResult('localStorage状态', data);
        }

        // 页面加载时自动测试当前期次API
        window.onload = function() {
            checkStorage();
            testCurrentPeriod();
        };
    </script>
</body>
</html>
