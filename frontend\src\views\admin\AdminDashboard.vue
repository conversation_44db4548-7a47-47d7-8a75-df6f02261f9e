<template>
  <div class="admin-dashboard">
    <!-- 页面头部 -->
    <div class="mb-8">
      <div class="flex items-center justify-between">
        <div>
          <h1 class="text-3xl font-bold text-white mb-2">数据仪表盘</h1>
          <p class="text-gray-400">欢迎回来，这里是您的平台运营数据概览</p>
        </div>
        <div class="flex items-center space-x-3">
          <button
            @click="refreshData"
            class="px-4 py-2 bg-gradient-to-r from-gold-500 to-gold-600 hover:from-gold-600 hover:to-gold-700 text-black font-medium rounded-lg transition-all duration-200 flex items-center space-x-2"
          >
            <el-icon><Refresh /></el-icon>
            <span>刷新数据</span>
          </button>
          <div class="relative">
            <button
              @click="showExportMenu = !showExportMenu"
              class="px-4 py-2 bg-gray-700 hover:bg-gray-600 text-gray-300 hover:text-white border border-gray-600 rounded-lg transition-all duration-200 flex items-center space-x-2"
            >
              <span>导出报告</span>
              <el-icon><ArrowDown /></el-icon>
            </button>
            <div v-if="showExportMenu" class="absolute right-0 mt-2 w-48 bg-gray-800 border border-gray-600 rounded-lg shadow-xl z-10">
              <button
                @click="handleExport('excel')"
                class="w-full px-4 py-2 text-left text-gray-300 hover:text-white hover:bg-gray-700 transition-colors"
              >
                导出Excel
              </button>
              <button
                @click="handleExport('pdf')"
                class="w-full px-4 py-2 text-left text-gray-300 hover:text-white hover:bg-gray-700 transition-colors"
              >
                导出PDF
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 核心指标卡片 -->
    <div class="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-4 gap-6 mb-8">
      <!-- 总用户数 -->
      <div class="bg-gradient-to-br from-gray-800 to-gray-900 border border-gold-500/30 rounded-xl shadow-xl p-6 text-white relative overflow-hidden group hover:border-gold-500/50 transition-all duration-300">
        <div class="absolute top-0 right-0 -mt-4 -mr-4 w-20 h-20 bg-gold-500/10 rounded-full"></div>
        <div class="absolute inset-0 bg-gradient-to-br from-blue-500/5 to-transparent"></div>
        <div class="relative z-10">
          <div class="flex items-center justify-between mb-4">
            <div class="p-3 bg-blue-500/20 rounded-lg border border-blue-500/30">
              <el-icon size="24" class="text-blue-400"><User /></el-icon>
            </div>
            <div class="text-right">
              <div class="text-gray-400 text-sm">较昨日</div>
              <div class="text-green-400 font-semibold">+12%</div>
            </div>
          </div>
          <div class="text-3xl font-bold mb-1 text-white">{{ formatNumber(stats?.totalUsers || 0) }}</div>
          <div class="text-gray-400 text-sm">总用户数</div>
        </div>
      </div>

      <!-- 总抽奖次数 -->
      <div class="bg-gradient-to-br from-gray-800 to-gray-900 border border-gold-500/30 rounded-xl shadow-xl p-6 text-white relative overflow-hidden group hover:border-gold-500/50 transition-all duration-300">
        <div class="absolute top-0 right-0 -mt-4 -mr-4 w-20 h-20 bg-gold-500/10 rounded-full"></div>
        <div class="absolute inset-0 bg-gradient-to-br from-green-500/5 to-transparent"></div>
        <div class="relative z-10">
          <div class="flex items-center justify-between mb-4">
            <div class="p-3 bg-green-500/20 rounded-lg border border-green-500/30">
              <el-icon size="24" class="text-green-400"><Trophy /></el-icon>
            </div>
            <div class="text-right">
              <div class="text-gray-400 text-sm">较昨日</div>
              <div class="text-green-400 font-semibold">+8%</div>
            </div>
          </div>
          <div class="text-3xl font-bold mb-1 text-white">{{ formatNumber(stats?.totalLotteryRecords || 0) }}</div>
          <div class="text-gray-400 text-sm">总抽奖次数</div>
        </div>
      </div>

      <!-- 总中奖金额 -->
      <div class="bg-gradient-to-br from-gray-800 to-gray-900 border border-gold-500/30 rounded-xl shadow-xl p-6 text-white relative overflow-hidden group hover:border-gold-500/50 transition-all duration-300">
        <div class="absolute top-0 right-0 -mt-4 -mr-4 w-20 h-20 bg-gold-500/10 rounded-full"></div>
        <div class="absolute inset-0 bg-gradient-to-br from-gold-500/5 to-transparent"></div>
        <div class="relative z-10">
          <div class="flex items-center justify-between mb-4">
            <div class="p-3 bg-gold-500/20 rounded-lg border border-gold-500/30">
              <el-icon size="24" class="text-gold-400"><Money /></el-icon>
            </div>
            <div class="text-right">
              <div class="text-gray-400 text-sm">较昨日</div>
              <div class="text-green-400 font-semibold">+15%</div>
            </div>
          </div>
          <div class="text-3xl font-bold mb-1 text-gold-400">¥{{ formatAmount(stats?.totalWinnings || 0) }}</div>
          <div class="text-gray-400 text-sm">总中奖金额</div>
        </div>
      </div>

      <!-- 今日参与人数 -->
      <div class="bg-gradient-to-br from-gray-800 to-gray-900 border border-gold-500/30 rounded-xl shadow-xl p-6 text-white relative overflow-hidden group hover:border-gold-500/50 transition-all duration-300">
        <div class="absolute top-0 right-0 -mt-4 -mr-4 w-20 h-20 bg-gold-500/10 rounded-full"></div>
        <div class="absolute inset-0 bg-gradient-to-br from-purple-500/5 to-transparent"></div>
        <div class="relative z-10">
          <div class="flex items-center justify-between mb-4">
            <div class="p-3 bg-purple-500/20 rounded-lg border border-purple-500/30">
              <el-icon size="24" class="text-purple-400"><Calendar /></el-icon>
            </div>
            <div class="text-right">
              <div class="text-gray-400 text-sm">较昨日</div>
              <div class="text-green-400 font-semibold">+5%</div>
            </div>
          </div>
          <div class="text-3xl font-bold mb-1 text-white">{{ formatNumber(stats?.todayParticipants || 0) }}</div>
          <div class="text-gray-400 text-sm">今日参与人数</div>
        </div>
      </div>
    </div>

    <!-- 主要功能区域 -->
    <div class="mb-8">
      <!-- 快速操作 -->
      <div class="bg-gradient-to-br from-gray-800 to-gray-900 border border-gold-500/30 rounded-xl shadow-xl p-6">
        <div class="flex items-center justify-between mb-6">
          <h3 class="text-xl font-semibold text-white">快速操作</h3>
          <div class="px-3 py-1 bg-gold-500/20 border border-gold-500/30 rounded-full">
            <span class="text-xs font-medium text-gold-400">管理功能</span>
          </div>
        </div>
        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-4">
          <div
            class="group cursor-pointer bg-gradient-to-br from-gray-700 to-gray-800 hover:from-gray-600 hover:to-gray-700 rounded-lg p-6 transition-all duration-300 border border-gray-600 hover:border-blue-500/50"
            @click="$router.push('/admin/users')"
          >
            <div class="text-center">
              <div class="w-12 h-12 bg-blue-500/20 border border-blue-500/30 rounded-lg flex items-center justify-center mx-auto mb-3 group-hover:scale-110 transition-transform">
                <el-icon class="text-blue-400" size="24"><User /></el-icon>
              </div>
              <h4 class="font-semibold text-white mb-1">用户管理</h4>
              <p class="text-sm text-gray-400">管理平台用户</p>
            </div>
          </div>

          <div
            class="group cursor-pointer bg-gradient-to-br from-gray-700 to-gray-800 hover:from-gray-600 hover:to-gray-700 rounded-lg p-6 transition-all duration-300 border border-gray-600 hover:border-green-500/50"
            @click="$router.push('/admin/lottery-periods')"
          >
            <div class="text-center">
              <div class="w-12 h-12 bg-green-500/20 border border-green-500/30 rounded-lg flex items-center justify-center mx-auto mb-3 group-hover:scale-110 transition-transform">
                <el-icon class="text-green-400" size="24"><Calendar /></el-icon>
              </div>
              <h4 class="font-semibold text-white mb-1">期次管理</h4>
              <p class="text-sm text-gray-400">管理抽奖期次</p>
            </div>
          </div>

          <div
            class="group cursor-pointer bg-gradient-to-br from-gray-700 to-gray-800 hover:from-gray-600 hover:to-gray-700 rounded-lg p-6 transition-all duration-300 border border-gray-600 hover:border-orange-500/50"
            @click="$router.push('/admin/lottery-records')"
          >
            <div class="text-center">
              <div class="w-12 h-12 bg-orange-500/20 border border-orange-500/30 rounded-lg flex items-center justify-center mx-auto mb-3 group-hover:scale-110 transition-transform">
                <el-icon class="text-orange-400" size="24"><List /></el-icon>
              </div>
              <h4 class="font-semibold text-white mb-1">抽奖记录</h4>
              <p class="text-sm text-gray-400">查看抽奖记录</p>
            </div>
          </div>

          <div
            class="group cursor-pointer bg-gradient-to-br from-gray-700 to-gray-800 hover:from-gray-600 hover:to-gray-700 rounded-lg p-6 transition-all duration-300 border border-gray-600 hover:border-purple-500/50"
            @click="$router.push('/admin/settings')"
          >
            <div class="text-center">
              <div class="w-12 h-12 bg-purple-500/20 border border-purple-500/30 rounded-lg flex items-center justify-center mx-auto mb-3 group-hover:scale-110 transition-transform">
                <el-icon class="text-purple-400" size="24"><Setting /></el-icon>
              </div>
              <h4 class="font-semibold text-white mb-1">系统设置</h4>
              <p class="text-sm text-gray-400">配置系统参数</p>
            </div>
          </div>

          <div
            class="group cursor-pointer bg-gradient-to-br from-gray-700 to-gray-800 hover:from-gray-600 hover:to-gray-700 rounded-lg p-6 transition-all duration-300 border border-gray-600 hover:border-red-500/50"
            @click="handleBackup"
          >
            <div class="text-center">
              <div class="w-12 h-12 bg-red-500/20 border border-red-500/30 rounded-lg flex items-center justify-center mx-auto mb-3 group-hover:scale-110 transition-transform">
                <el-icon class="text-red-400" size="24"><Download /></el-icon>
              </div>
              <h4 class="font-semibold text-white mb-1">数据备份</h4>
              <p class="text-sm text-gray-400">备份系统数据</p>
            </div>
          </div>

          <div
            class="group cursor-pointer bg-gradient-to-br from-gray-700 to-gray-800 hover:from-gray-600 hover:to-gray-700 rounded-lg p-6 transition-all duration-300 border border-gray-600 hover:border-gray-500/50"
            @click="handleLogs"
          >
            <div class="text-center">
              <div class="w-12 h-12 bg-gray-500/20 border border-gray-500/30 rounded-lg flex items-center justify-center mx-auto mb-3 group-hover:scale-110 transition-transform">
                <el-icon class="text-gray-400" size="24"><Document /></el-icon>
              </div>
              <h4 class="font-semibold text-white mb-1">系统日志</h4>
              <p class="text-sm text-gray-400">查看系统日志</p>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 最近活动 -->
    <div class="bg-gradient-to-br from-gray-800 to-gray-900 border border-gold-500/30 rounded-xl shadow-xl p-6">
      <div class="flex items-center justify-between mb-6">
        <h3 class="text-xl font-semibold text-white">最近活动</h3>
        <button
          @click="$router.push('/admin/lottery-records')"
          class="px-3 py-1 bg-gold-500/20 hover:bg-gold-500/30 border border-gold-500/30 text-gold-400 hover:text-gold-300 rounded-lg transition-all text-sm"
        >
          查看全部
        </button>
      </div>
      <div class="space-y-4">
        <div v-if="recentActivities.length === 0" class="text-center py-8">
          <div class="text-gray-400 mb-2">
            <el-icon size="48"><Bell /></el-icon>
          </div>
          <p class="text-gray-400">暂无最近活动</p>
        </div>
        <div v-else v-for="activity in recentActivities" :key="activity.id" class="flex items-center space-x-4 p-4 bg-gray-700/30 rounded-lg hover:bg-gray-700/50 transition-colors border border-gray-600/50">
          <div class="flex-shrink-0">
            <div :class="activity.iconClass" class="w-10 h-10 rounded-full flex items-center justify-center border">
              <el-icon :class="activity.iconColor" size="18">
                <component :is="activity.icon" />
              </el-icon>
            </div>
          </div>
          <div class="flex-1 min-w-0">
            <p class="text-sm font-medium text-white truncate">{{ activity.title }}</p>
            <p class="text-sm text-gray-400">{{ activity.description }}</p>
          </div>
          <div class="flex-shrink-0 text-sm text-gray-500">
            {{ activity.time }}
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useAdminStore } from '../../stores/admin'

const adminStore = useAdminStore()
const stats = ref(adminStore.stats)

// 响应式数据
const showExportMenu = ref(false)

// 最近活动数据
interface Activity {
  id: string
  title: string
  description: string
  time: string
  type: string
  timestamp: string
  icon?: string
  iconClass?: string
  iconColor?: string
}

const recentActivities = ref<Activity[]>([])

// 格式化数字
const formatNumber = (num: number) => {
  if (num >= 1000000) {
    return (num / 1000000).toFixed(1) + 'M'
  } else if (num >= 1000) {
    return (num / 1000).toFixed(1) + 'K'
  }
  return num.toString()
}

// 格式化金额
const formatAmount = (amount: number) => {
  return amount.toLocaleString('zh-CN', { minimumFractionDigits: 2 })
}

// 刷新数据
const refreshData = async () => {
  ElMessage.info('正在刷新数据...')
  await Promise.all([loadStats(), loadRecentActivities()])
  ElMessage.success('数据刷新完成')
}

// 导出报告
const handleExport = (command: string) => {
  ElMessage.info(`正在导出${command === 'excel' ? 'Excel' : 'PDF'}报告...`)
  // 这里可以实现实际的导出逻辑
}

// 数据备份
const handleBackup = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要进行数据备份吗？此操作可能需要几分钟时间。',
      '数据备份',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )

    ElMessage.success('数据备份已开始，请稍候...')
    // 这里可以实现实际的备份逻辑
  } catch {
    // 用户取消
  }
}

// 查看日志
const handleLogs = () => {
  ElMessage.info('系统日志功能开发中...')
  // 这里可以跳转到日志页面
}

// 加载统计数据
const loadStats = async () => {
  const result = await adminStore.fetchStats()
  if (result.success) {
    stats.value = adminStore.stats
  } else {
    ElMessage.error(result.message || '加载统计数据失败')
  }
}

// 加载最近活动
const loadRecentActivities = async () => {
  const result = await adminStore.fetchRecentActivities(8)
  if (result.success) {
    recentActivities.value = result.data.map((activity: any) => ({
      ...activity,
      icon: getActivityIcon(activity.type),
      iconClass: getActivityIconClass(activity.type),
      iconColor: getActivityIconColor(activity.type)
    }))
  } else {
    ElMessage.error(result.message || '加载最近活动失败')
  }
}

// 获取活动图标
const getActivityIcon = (type: string) => {
  switch (type) {
    case 'lottery_win': return 'Trophy'
    case 'lottery_participate': return 'Tickets'
    case 'user_register': return 'User'
    case 'period_update': return 'Calendar'
    default: return 'Bell'
  }
}

// 获取活动图标样式类
const getActivityIconClass = (type: string) => {
  switch (type) {
    case 'lottery_win': return 'bg-yellow-500/20 border-yellow-500/30'
    case 'lottery_participate': return 'bg-blue-500/20 border-blue-500/30'
    case 'user_register': return 'bg-green-500/20 border-green-500/30'
    case 'period_update': return 'bg-purple-500/20 border-purple-500/30'
    default: return 'bg-gray-500/20 border-gray-500/30'
  }
}

// 获取活动图标颜色
const getActivityIconColor = (type: string) => {
  switch (type) {
    case 'lottery_win': return 'text-yellow-400'
    case 'lottery_participate': return 'text-blue-400'
    case 'user_register': return 'text-green-400'
    case 'period_update': return 'text-purple-400'
    default: return 'text-gray-400'
  }
}

onMounted(() => {
  loadStats()
  loadRecentActivities()
})
</script>

<style scoped>
.admin-dashboard {
  max-width: 1400px;
  margin: 0 auto;
}

/* 深色主题金色装饰 */
.text-gold-400 {
  color: #d4af37;
}

.bg-gold-500 {
  background-color: #d4af37;
}

.border-gold-500 {
  border-color: #d4af37;
}

/* 卡片悬停效果 */
.group:hover {
  transform: translateY(-2px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

/* 渐变背景动画 */
@keyframes gradient {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

.bg-gradient-to-br {
  background-size: 200% 200%;
  animation: gradient 8s ease infinite;
}

/* 脉冲动画 */
@keyframes pulse-dot {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.animate-pulse {
  animation: pulse-dot 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* 发光效果 */
@keyframes glow {
  0%, 100% {
    box-shadow: 0 0 5px rgba(212, 175, 55, 0.3);
  }
  50% {
    box-shadow: 0 0 20px rgba(212, 175, 55, 0.6);
  }
}

.group:hover {
  animation: glow 2s ease-in-out infinite;
}

/* 自定义滚动条 */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.1);
}

::-webkit-scrollbar-thumb {
  background: rgba(212, 175, 55, 0.3);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(212, 175, 55, 0.5);
}

/* Element Plus 组件样式覆盖 */
.el-button .el-icon {
  margin-right: 0 !important;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .admin-dashboard {
    padding: 0 16px;
  }

  .grid {
    gap: 16px;
  }

  .text-3xl {
    font-size: 1.875rem;
  }

  .xl\\:grid-cols-4 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .lg\\:grid-cols-3 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }
}

@media (max-width: 640px) {
  .xl\\:grid-cols-4,
  .lg\\:grid-cols-3 {
    grid-template-columns: repeat(1, minmax(0, 1fr));
  }
}
</style>
