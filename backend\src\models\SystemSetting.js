const { DataTypes } = require('sequelize')
const { sequelize } = require('../config/database')

const SystemSetting = sequelize.define('SystemSetting', {
  id: {
    type: DataTypes.BIGINT,
    primaryKey: true,
    autoIncrement: true
  },
  setting_key: {
    type: DataTypes.STRING(100),
    allowNull: false,
    unique: true,
    field: 'setting_key'
  },
  setting_value: {
    type: DataTypes.TEXT,
    allowNull: true,
    field: 'setting_value'
  },
  setting_type: {
    type: DataTypes.ENUM('string', 'number', 'boolean', 'json'),
    defaultValue: 'string',
    field: 'setting_type'
  },
  description: {
    type: DataTypes.STRING(255),
    allowNull: true
  },
  is_active: {
    type: DataTypes.BOOLEAN,
    defaultValue: true,
    field: 'is_active'
  },
  created_at: {
    type: DataTypes.DATE,
    allowNull: false,
    field: 'created_at'
  },
  updated_at: {
    type: DataTypes.DATE,
    allowNull: false,
    field: 'updated_at'
  }
}, {
  tableName: 'system_settings',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at'
})

// 辅助方法：获取设置值并转换类型
SystemSetting.getValue = async function(key, defaultValue = null) {
  try {
    const setting = await this.findOne({
      where: { 
        setting_key: key,
        is_active: true
      }
    })
    
    if (!setting) return defaultValue
    
    const value = setting.setting_value
    const type = setting.setting_type
    
    switch (type) {
      case 'number':
        return parseFloat(value) || defaultValue
      case 'boolean':
        return value === 'true' || value === '1'
      case 'json':
        try {
          return JSON.parse(value)
        } catch {
          return defaultValue
        }
      default:
        return value || defaultValue
    }
  } catch (error) {
    console.error('Error getting setting value:', error)
    return defaultValue
  }
}

// 辅助方法：设置值
SystemSetting.setValue = async function(key, value, type = 'string') {
  try {
    let stringValue = value
    
    if (type === 'json') {
      stringValue = JSON.stringify(value)
    } else if (type === 'boolean') {
      stringValue = value ? 'true' : 'false'
    } else {
      stringValue = String(value)
    }
    
    const [setting, created] = await this.findOrCreate({
      where: { setting_key: key },
      defaults: {
        setting_key: key,
        setting_value: stringValue,
        setting_type: type,
        is_active: true
      }
    })
    
    if (!created) {
      await setting.update({
        setting_value: stringValue,
        setting_type: type
      })
    }
    
    return setting
  } catch (error) {
    console.error('Error setting value:', error)
    throw error
  }
}

module.exports = SystemSetting
