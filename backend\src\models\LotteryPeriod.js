const { DataTypes } = require('sequelize')
const { sequelize } = require('../config/database')

// 抽奖期次模型
const LotteryPeriod = sequelize.define('LotteryPeriod', {
  id: {
    type: DataTypes.BIGINT,
    primaryKey: true,
    autoIncrement: true
  },
  periodName: {
    type: DataTypes.STRING(100),
    allowNull: false,
    field: 'period_name'
  },
  periodNumber: {
    type: DataTypes.INTEGER,
    allowNull: false,
    field: 'period_number'
  },
  startTime: {
    type: DataTypes.DATE,
    allowNull: false,
    field: 'start_time'
  },
  endTime: {
    type: DataTypes.DATE,
    allowNull: false,
    field: 'end_time'
  },
  drawTime: {
    type: DataTypes.DATE,
    allowNull: false,
    field: 'draw_time'
  },
  status: {
    type: DataTypes.ENUM('未开始', '进行中', '已结束', '已开奖'),
    defaultValue: '未开始'
  },
  maxAttemptsPerUser: {
    type: DataTypes.INTEGER,
    defaultValue: 3,
    field: 'max_attempts_per_user'
  },
  totalParticipants: {
    type: DataTypes.INTEGER,
    defaultValue: 0,
    field: 'total_participants'
  },
  totalAttempts: {
    type: DataTypes.INTEGER,
    defaultValue: 0,
    field: 'total_attempts'
  },
  totalPrizeAmount: {
    type: DataTypes.DECIMAL(15, 2),
    defaultValue: 0,
    field: 'total_prize_amount',
    comment: '本期抽奖总金额'
  },
  actualDrawableAmount: {
    type: DataTypes.DECIMAL(15, 2),
    defaultValue: 0,
    field: 'actual_drawable_amount',
    comment: '实际可抽取金额'
  },
  totalShares: {
    type: DataTypes.INTEGER,
    defaultValue: 0,
    field: 'total_shares',
    comment: '总份数'
  },
  settings: {
    type: DataTypes.JSON
  }
}, {
  tableName: 'lottery_periods',
  underscored: true
})

module.exports = LotteryPeriod
