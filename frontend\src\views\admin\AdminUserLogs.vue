<template>
  <div class="admin-user-logs min-h-screen bg-gradient-to-br from-gray-900 via-black to-gray-800 p-6">
    <!-- 页面头部 -->
    <div class="mb-8">
      <div class="flex items-center justify-between">
        <div>
          <h2 class="text-3xl font-bold text-white mb-2 flex items-center">
            <el-icon class="mr-3 text-gold-400" size="32"><Document /></el-icon>
            用户操作日志
          </h2>
          <p class="text-gray-400">查看用户在系统中的操作记录</p>
        </div>
        <div class="flex items-center space-x-4">
          <div class="px-4 py-2 bg-gradient-to-r from-gold-500/20 to-gold-600/20 border border-gold-500/30 rounded-lg">
            <span class="text-gold-400 text-sm font-medium">总记录数: {{ filteredLogs.length }}</span>
          </div>
          <button
            @click="handleRefresh"
            class="px-4 py-2 bg-blue-500/20 hover:bg-blue-500/30 border border-blue-500/30 text-blue-400 hover:text-blue-300 rounded-lg transition-all duration-200 flex items-center space-x-2"
          >
            <el-icon><Refresh /></el-icon>
            <span>刷新数据</span>
          </button>
        </div>
      </div>
    </div>

    <!-- 搜索和筛选 -->
    <div class="bg-gradient-to-br from-gray-800 to-gray-900 border border-gold-500/30 rounded-xl shadow-xl p-6 mb-6">
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <input
          v-model="searchKeyword"
          type="text"
          placeholder="搜索用户名或平台ID"
          class="px-4 py-3 bg-gray-700 border border-gray-600 text-white placeholder-gray-400 rounded-lg focus:outline-none focus:border-gold-500 focus:ring-1 focus:ring-gold-500 transition-all"
        />

        <select 
          v-model="actionFilter" 
          class="px-4 py-3 bg-gray-700 border border-gray-600 text-white rounded-lg focus:outline-none focus:border-gold-500 min-w-[150px]"
        >
          <option value="">全部操作类型</option>
          <option value="登录">登录</option>
          <option value="注册">注册</option>
          <option value="抽奖">抽奖</option>
          <option value="查看记录">查看记录</option>
          <option value="修改资料">修改资料</option>
        </select>

        <input
          v-model="dateFilter"
          type="date"
          class="px-4 py-3 bg-gray-700 border border-gray-600 text-white rounded-lg focus:outline-none focus:border-gold-500 focus:ring-1 focus:ring-gold-500 transition-all"
        />

        <button
          @click="clearFilters"
          class="px-4 py-3 bg-gray-600 hover:bg-gray-500 text-white rounded-lg transition-all duration-200 flex items-center justify-center space-x-2"
        >
          <el-icon><Close /></el-icon>
          <span>清除筛选</span>
        </button>
      </div>
    </div>

    <!-- 日志列表 -->
    <div class="bg-gradient-to-br from-gray-800 to-gray-900 border border-gold-500/30 rounded-xl shadow-xl overflow-hidden">
      <!-- 表格头部 -->
      <div class="px-6 py-4 border-b border-gray-700">
        <h3 class="text-lg font-semibold text-white flex items-center">
          <el-icon class="mr-2 text-gold-400"><List /></el-icon>
          操作日志列表
        </h3>
      </div>

      <!-- 加载状态 -->
      <div v-if="isLoading" class="flex items-center justify-center py-12">
        <div class="text-center">
          <div class="w-8 h-8 border-2 border-gold-500 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p class="text-gray-400">加载中...</p>
        </div>
      </div>

      <!-- 表格内容 -->
      <div v-else-if="paginatedLogs.length > 0" class="overflow-x-auto">
        <table class="w-full">
          <thead class="bg-gray-700/50">
            <tr>
              <th class="px-6 py-4 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">日志ID</th>
              <th class="px-6 py-4 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">用户信息</th>
              <th class="px-6 py-4 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">操作类型</th>
              <th class="px-6 py-4 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">操作描述</th>
              <th class="px-6 py-4 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">IP地址</th>
              <th class="px-6 py-4 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">操作时间</th>
              <th class="px-6 py-4 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">操作</th>
            </tr>
          </thead>
          <tbody class="divide-y divide-gray-700">
            <tr 
              v-for="log in paginatedLogs" 
              :key="log.id"
              class="hover:bg-gray-700/30 transition-colors"
            >
              <!-- 日志ID -->
              <td class="px-6 py-4 whitespace-nowrap">
                <span class="text-gold-400 font-medium">#{{ log.id }}</span>
              </td>
              
              <!-- 用户信息 -->
              <td class="px-6 py-4 whitespace-nowrap">
                <div>
                  <div class="text-white font-medium">{{ log.user?.username || '未知用户' }}</div>
                  <div class="text-gray-400 text-sm">ID: {{ log.user?.platformId || log.platformId }}</div>
                </div>
              </td>
              
              <!-- 操作类型 -->
              <td class="px-6 py-4 whitespace-nowrap">
                <span :class="getActionColor(log.action)" class="px-2 py-1 border rounded text-sm font-medium">
                  {{ log.action }}
                </span>
              </td>
              
              <!-- 操作描述 -->
              <td class="px-6 py-4">
                <div class="text-gray-300 text-sm max-w-xs truncate" :title="log.description">
                  {{ log.description }}
                </div>
              </td>
              
              <!-- IP地址 -->
              <td class="px-6 py-4 whitespace-nowrap text-gray-300 text-sm font-mono">
                {{ log.ipAddress }}
              </td>
              
              <!-- 操作时间 -->
              <td class="px-6 py-4 whitespace-nowrap text-gray-300 text-sm">
                {{ formatDate(log.createdAt) }}
              </td>
              
              <!-- 操作 -->
              <td class="px-6 py-4 whitespace-nowrap">
                <button
                  @click="handleViewDetails(log)"
                  class="px-3 py-1 bg-blue-500/20 hover:bg-blue-500/30 border border-blue-500/30 text-blue-400 hover:text-blue-300 rounded transition-all text-sm"
                >
                  查看详情
                </button>
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      <!-- 空状态 -->
      <div v-else class="flex items-center justify-center py-12">
        <div class="text-center">
          <el-icon size="64" class="text-gray-600 mb-4"><Document /></el-icon>
          <p class="text-gray-400 text-lg mb-2">暂无操作日志</p>
          <p class="text-gray-500 text-sm">请检查搜索条件或稍后重试</p>
        </div>
      </div>

      <!-- 分页 -->
      <div v-if="filteredLogs.length > 0" class="px-6 py-4 border-t border-gray-700 flex justify-between items-center">
        <div class="text-gray-400 text-sm">
          共 {{ filteredLogs.length }} 条记录，每页显示 {{ pageSize }} 条
        </div>
        <div class="flex items-center space-x-2">
          <button
            @click="currentPage = Math.max(1, currentPage - 1)"
            :disabled="currentPage === 1"
            class="px-3 py-1 bg-gray-700 hover:bg-gray-600 disabled:bg-gray-800 disabled:text-gray-600 text-gray-300 rounded transition-all"
          >
            上一页
          </button>
          <span class="px-3 py-1 bg-gold-500/20 border border-gold-500/30 text-gold-400 rounded">
            {{ currentPage }} / {{ totalPages }}
          </span>
          <button
            @click="currentPage = Math.min(totalPages, currentPage + 1)"
            :disabled="currentPage === totalPages"
            class="px-3 py-1 bg-gray-700 hover:bg-gray-600 disabled:bg-gray-800 disabled:text-gray-600 text-gray-300 rounded transition-all"
          >
            下一页
          </button>
        </div>
      </div>
    </div>

    <!-- 详情对话框 -->
    <div v-if="detailDialogVisible" class="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
      <div class="bg-gradient-to-br from-gray-800 to-gray-900 border border-gold-500/30 rounded-xl shadow-2xl w-full max-w-2xl max-h-[90vh] overflow-y-auto">
        <!-- 对话框头部 -->
        <div class="flex items-center justify-between p-6 border-b border-gray-700">
          <h3 class="text-xl font-semibold text-white flex items-center">
            <el-icon class="mr-3 text-gold-400" size="24"><Document /></el-icon>
            操作日志详情
          </h3>
          <button
            @click="detailDialogVisible = false"
            class="p-2 text-gray-400 hover:text-white hover:bg-gray-700 rounded-lg transition-all"
          >
            <el-icon size="20"><Close /></el-icon>
          </button>
        </div>

        <!-- 详情内容 -->
        <div v-if="selectedLog" class="p-6">
          <div class="space-y-4">
            <div>
              <label class="block text-sm font-medium text-gray-400 mb-2">日志ID</label>
              <p class="text-white font-medium">#{{ selectedLog.id }}</p>
            </div>
            
            <div>
              <label class="block text-sm font-medium text-gray-400 mb-2">用户信息</label>
              <div class="text-white">
                <div>用户名: {{ selectedLog.user?.username || '未知用户' }}</div>
                <div class="text-gray-400 text-sm">平台ID: {{ selectedLog.user?.platformId || selectedLog.platformId }}</div>
              </div>
            </div>
            
            <div>
              <label class="block text-sm font-medium text-gray-400 mb-2">操作类型</label>
              <span :class="getActionColor(selectedLog.action)" class="px-3 py-1 border rounded text-sm font-medium">
                {{ selectedLog.action }}
              </span>
            </div>
            
            <div>
              <label class="block text-sm font-medium text-gray-400 mb-2">操作描述</label>
              <p class="text-gray-300">{{ selectedLog.description }}</p>
            </div>
            
            <div>
              <label class="block text-sm font-medium text-gray-400 mb-2">IP地址</label>
              <p class="text-gray-300 font-mono">{{ selectedLog.ipAddress }}</p>
            </div>
            
            <div>
              <label class="block text-sm font-medium text-gray-400 mb-2">用户代理</label>
              <p class="text-gray-300 text-sm break-all">{{ selectedLog.userAgent || '未记录' }}</p>
            </div>
            
            <div>
              <label class="block text-sm font-medium text-gray-400 mb-2">操作时间</label>
              <p class="text-gray-300">{{ formatDate(selectedLog.createdAt) }}</p>
            </div>
            
            <div v-if="selectedLog.details">
              <label class="block text-sm font-medium text-gray-400 mb-2">详细信息</label>
              <pre class="text-gray-300 text-sm bg-gray-700/50 p-3 rounded border border-gray-600 overflow-x-auto">{{ JSON.stringify(selectedLog.details, null, 2) }}</pre>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Document, List, Refresh, Close } from '@element-plus/icons-vue'
import { useAdminStore } from '../../stores/admin'

const adminStore = useAdminStore()

// 搜索和筛选
const searchKeyword = ref('')
const actionFilter = ref('')
const dateFilter = ref('')
const currentPage = ref(1)
const pageSize = ref(20)
const isLoading = ref(false)

// 对话框
const detailDialogVisible = ref(false)
const selectedLog = ref(null)

// 模拟数据
const mockLogs = ref([
  {
    id: 1,
    user: { username: 'user001', platformId: 'PID001' },
    platformId: 'PID001',
    action: '登录',
    description: '用户成功登录系统',
    ipAddress: '*************',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
    createdAt: '2024-01-15 14:30:00',
    details: { loginMethod: 'password', success: true }
  },
  {
    id: 2,
    user: { username: 'user002', platformId: 'PID002' },
    platformId: 'PID002',
    action: '抽奖',
    description: '参与第1期抽奖活动',
    ipAddress: '*************',
    userAgent: 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X)',
    createdAt: '2024-01-15 14:35:00',
    details: { periodId: 1, result: 'win', prize: '三等奖' }
  },
  {
    id: 3,
    user: { username: 'user003', platformId: 'PID003' },
    platformId: 'PID003',
    action: '查看记录',
    description: '查看个人抽奖记录',
    ipAddress: '*************',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7)',
    createdAt: '2024-01-15 14:40:00',
    details: { pageViewed: 'records', recordCount: 5 }
  }
])

// 过滤后的日志列表
const filteredLogs = computed(() => {
  let logs = mockLogs.value

  // 搜索关键词筛选
  if (searchKeyword.value) {
    const keyword = searchKeyword.value.toLowerCase()
    logs = logs.filter(log =>
      log.user?.username?.toLowerCase().includes(keyword) ||
      log.user?.platformId?.toLowerCase().includes(keyword) ||
      log.platformId?.toLowerCase().includes(keyword)
    )
  }

  // 操作类型筛选
  if (actionFilter.value) {
    logs = logs.filter(log => log.action === actionFilter.value)
  }

  // 日期筛选
  if (dateFilter.value) {
    logs = logs.filter(log => {
      const logDate = new Date(log.createdAt).toISOString().split('T')[0]
      return logDate === dateFilter.value
    })
  }

  return logs.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())
})

// 分页计算
const totalPages = computed(() => Math.ceil(filteredLogs.value.length / pageSize.value))
const paginatedLogs = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value
  const end = start + pageSize.value
  return filteredLogs.value.slice(start, end)
})

// 格式化日期
const formatDate = (dateString: string) => {
  if (!dateString) return '未记录'
  return new Date(dateString).toLocaleString('zh-CN')
}

// 获取操作类型颜色
const getActionColor = (action: string) => {
  switch (action) {
    case '登录': return 'bg-green-500/20 border-green-500/30 text-green-400'
    case '注册': return 'bg-blue-500/20 border-blue-500/30 text-blue-400'
    case '抽奖': return 'bg-purple-500/20 border-purple-500/30 text-purple-400'
    case '查看记录': return 'bg-yellow-500/20 border-yellow-500/30 text-yellow-400'
    case '修改资料': return 'bg-orange-500/20 border-orange-500/30 text-orange-400'
    default: return 'bg-gray-500/20 border-gray-500/30 text-gray-400'
  }
}

// 清除筛选
const clearFilters = () => {
  searchKeyword.value = ''
  actionFilter.value = ''
  dateFilter.value = ''
  currentPage.value = 1
}

// 刷新数据
const handleRefresh = async () => {
  isLoading.value = true
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    ElMessage.success('日志数据已刷新')
  } finally {
    isLoading.value = false
  }
}

// 查看详情
const handleViewDetails = (log: any) => {
  selectedLog.value = log
  detailDialogVisible.value = true
}

// 监听筛选条件变化，重置分页
const resetPagination = () => {
  currentPage.value = 1
}

// 监听器
const unwatchSearch = computed(() => searchKeyword.value)
const unwatchAction = computed(() => actionFilter.value)
const unwatchDate = computed(() => dateFilter.value)

// 当筛选条件变化时重置分页
const watchFilters = () => {
  resetPagination()
}

onMounted(() => {
  // 初始化数据
})
</script>
