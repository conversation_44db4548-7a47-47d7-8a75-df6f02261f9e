const http = require('http');

function testRegister() {
  const timestamp = Date.now();
  const postData = JSON.stringify({
    platformId: 'test_' + timestamp,
    username: 'testuser_' + timestamp,
    password: '123456'
  });

  const options = {
    hostname: 'localhost',
    port: 3000,
    path: '/api/auth/register',
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Content-Length': Buffer.byteLength(postData)
    }
  };

  const req = http.request(options, (res) => {
    let data = '';

    res.on('data', (chunk) => {
      data += chunk;
    });

    res.on('end', () => {
      try {
        const response = JSON.parse(data);
        if (res.statusCode === 201) {
          console.log('✅ 注册成功:', response);
        } else {
          console.error('❌ 注册失败:');
          console.error('状态码:', res.statusCode);
          console.error('错误信息:', response);
        }
      } catch (error) {
        console.error('❌ 解析响应失败:', data);
      }
    });
  });

  req.on('error', (error) => {
    console.error('❌ 请求失败:', error.message);
  });

  req.write(postData);
  req.end();
}

console.log('🧪 开始测试用户注册...');
testRegister();
