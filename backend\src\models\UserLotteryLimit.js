const { DataTypes } = require('sequelize')
const { sequelize } = require('../config/database')

// 用户抽奖限制模型
const UserLotteryLimit = sequelize.define('UserLotteryLimit', {
  id: {
    type: DataTypes.BIGINT,
    primaryKey: true,
    autoIncrement: true
  },
  periodId: {
    type: DataTypes.BIGINT,
    allowNull: false,
    field: 'period_id'
  },
  userId: {
    type: DataTypes.BIGINT,
    allowNull: false,
    field: 'user_id'
  },
  platformId: {
    type: DataTypes.STRING(50),
    allowNull: false,
    field: 'platform_id'
  },
  maxAttempts: {
    type: DataTypes.INTEGER,
    allowNull: false,
    field: 'max_attempts'
  },
  usedAttempts: {
    type: DataTypes.INTEGER,
    defaultValue: 0,
    field: 'used_attempts'
  },
  lastAttemptAt: {
    type: DataTypes.DATE,
    field: 'last_attempt_at'
  }
}, {
  tableName: 'user_lottery_limits',
  underscored: true
})

module.exports = UserLotteryLimit
