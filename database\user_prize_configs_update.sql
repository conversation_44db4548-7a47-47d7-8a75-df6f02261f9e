-- =====================================================
-- 用户个人抽奖率配置系统 - 数据库更新脚本
-- 创建时间: 2025-01-28
-- 说明: 为用户添加个人抽奖率控制功能
-- =====================================================

-- 1. 为users表添加个人抽奖率总开关字段（如果不存在）
ALTER TABLE `users`
ADD COLUMN IF NOT EXISTS `custom_lottery_enabled` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否启用个人抽奖率设置(0:使用全局设置, 1:使用个人设置)' AFTER `updated_at`;

-- 2. 创建用户个人抽奖率配置表（如果不存在）
DROP TABLE IF EXISTS `user_prize_configs`;
CREATE TABLE `user_prize_configs` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `prize_config_id` bigint NOT NULL COMMENT '奖品配置ID',
  `custom_probability` decimal(5,4) NOT NULL DEFAULT '0.0000' COMMENT '个人中奖概率(0-1)',
  `is_enabled` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否启用个人设置(0:禁用, 1:启用)',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_user_prize` (`user_id`, `prize_config_id`) COMMENT '用户-奖品唯一索引',
  KEY `idx_user_id` (`user_id`) COMMENT '用户ID索引',
  KEY `idx_prize_config_id` (`prize_config_id`) COMMENT '奖品配置ID索引',
  KEY `idx_enabled` (`is_enabled`) COMMENT '启用状态索引',
  CONSTRAINT `fk_user_prize_configs_user_id` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_user_prize_configs_prize_config_id` FOREIGN KEY (`prize_config_id`) REFERENCES `prize_configs` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='用户个人抽奖率配置表';

-- 3. 为现有用户创建默认的个人抽奖率配置
-- 注意：这个脚本会为所有现有用户创建默认配置，概率与全局设置一致
INSERT INTO `user_prize_configs` (`user_id`, `prize_config_id`, `custom_probability`, `is_enabled`)
SELECT 
    u.id as user_id,
    pc.id as prize_config_id,
    pc.probability as custom_probability,
    1 as is_enabled
FROM `users` u
CROSS JOIN `prize_configs` pc
WHERE NOT EXISTS (
    SELECT 1 FROM `user_prize_configs` upc 
    WHERE upc.user_id = u.id AND upc.prize_config_id = pc.id
);

-- 4. 创建触发器：当新用户注册时自动创建个人抽奖率配置
DROP TRIGGER IF EXISTS `tr_users_after_insert`;

DELIMITER $$

CREATE TRIGGER `tr_users_after_insert`
AFTER INSERT ON `users`
FOR EACH ROW
BEGIN
    -- 为新用户创建所有奖品的个人配置，概率与全局设置一致
    INSERT INTO `user_prize_configs` (`user_id`, `prize_config_id`, `custom_probability`, `is_enabled`)
    SELECT 
        NEW.id as user_id,
        pc.id as prize_config_id,
        pc.probability as custom_probability,
        1 as is_enabled
    FROM `prize_configs` pc;
END$$

DELIMITER ;

-- 5. 创建触发器：当新增奖品配置时，为所有用户创建对应的个人配置
DROP TRIGGER IF EXISTS `tr_prize_configs_after_insert`;

DELIMITER $$

CREATE TRIGGER `tr_prize_configs_after_insert`
AFTER INSERT ON `prize_configs`
FOR EACH ROW
BEGIN
    -- 为所有用户创建新奖品的个人配置
    INSERT INTO `user_prize_configs` (`user_id`, `prize_config_id`, `custom_probability`, `is_enabled`)
    SELECT 
        u.id as user_id,
        NEW.id as prize_config_id,
        NEW.probability as custom_probability,
        1 as is_enabled
    FROM `users` u;
END$$

DELIMITER ;

-- 6. 创建存储过程：一键设置用户不中奖（除了"谢谢参与"）
DROP PROCEDURE IF EXISTS `sp_set_user_no_win`;

DELIMITER $$

CREATE PROCEDURE `sp_set_user_no_win`(
    IN p_user_id BIGINT,
    IN p_admin_id BIGINT COMMENT '操作管理员ID'
)
BEGIN
    DECLARE v_thank_you_prize_id BIGINT DEFAULT NULL;
    DECLARE v_affected_rows INT DEFAULT 0;
    
    -- 查找"谢谢参与"奖品ID（假设奖品名称包含"谢谢"或"参与"）
    SELECT id INTO v_thank_you_prize_id 
    FROM prize_configs 
    WHERE prize_name LIKE '%谢谢%' OR prize_name LIKE '%参与%' 
    LIMIT 1;
    
    -- 开始事务
    START TRANSACTION;
    
    -- 将除"谢谢参与"外的所有奖品概率设为0
    UPDATE user_prize_configs 
    SET custom_probability = 0.0000,
        updated_at = CURRENT_TIMESTAMP
    WHERE user_id = p_user_id 
      AND (v_thank_you_prize_id IS NULL OR prize_config_id != v_thank_you_prize_id);
    
    SET v_affected_rows = ROW_COUNT();
    
    -- 如果找到"谢谢参与"奖品，将其概率设为1（100%）
    IF v_thank_you_prize_id IS NOT NULL THEN
        UPDATE user_prize_configs 
        SET custom_probability = 1.0000,
            updated_at = CURRENT_TIMESTAMP
        WHERE user_id = p_user_id 
          AND prize_config_id = v_thank_you_prize_id;
    END IF;
    
    -- 启用用户的个人抽奖率设置
    UPDATE users 
    SET custom_lottery_enabled = 1,
        updated_at = CURRENT_TIMESTAMP
    WHERE id = p_user_id;
    
    -- 记录管理员操作日志
    INSERT INTO admin_logs (admin_id, action, description, risk_level, ip_address, details, created_at, updated_at)
    VALUES (
        p_admin_id,
        '用户抽奖设置',
        CONCAT('设置用户ID:', p_user_id, '为不中奖状态，影响', v_affected_rows, '个奖品配置'),
        '中',
        '127.0.0.1',
        JSON_OBJECT(
            'user_id', p_user_id,
            'action_type', 'SET_USER_NO_WIN',
            'affected_rows', v_affected_rows,
            'timestamp', NOW()
        ),
        CURRENT_TIMESTAMP,
        CURRENT_TIMESTAMP
    );
    
    COMMIT;
    
    SELECT 'SUCCESS' as status, v_affected_rows as affected_rows;
    
END$$

DELIMITER ;

-- 7. 创建视图：用户抽奖率配置详情视图
DROP VIEW IF EXISTS `v_user_prize_configs`;
CREATE VIEW `v_user_prize_configs` AS
SELECT
    upc.id,
    upc.user_id,
    u.username,
    u.custom_lottery_enabled,
    upc.prize_config_id,
    pc.prize_name,
    pc.prize_amount,
    pc.probability as global_probability,
    upc.custom_probability,
    upc.is_enabled,
    upc.created_at,
    upc.updated_at,
    CASE 
        WHEN u.custom_lottery_enabled = 1 AND upc.is_enabled = 1 THEN upc.custom_probability
        ELSE pc.probability
    END as effective_probability
FROM user_prize_configs upc
JOIN users u ON upc.user_id = u.id
JOIN prize_configs pc ON upc.prize_config_id = pc.id;

-- 8. 添加索引优化查询性能
CREATE INDEX `idx_users_custom_lottery_enabled` ON `users` (`custom_lottery_enabled`);
CREATE INDEX `idx_user_prize_configs_effective` ON `user_prize_configs` (`user_id`, `is_enabled`);

-- =====================================================
-- 脚本执行完成
-- =====================================================
