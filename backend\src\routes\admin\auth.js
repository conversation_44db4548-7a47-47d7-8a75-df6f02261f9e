const express = require('express')
const bcrypt = require('bcryptjs')
const jwt = require('jsonwebtoken')
const { Admin } = require('../../models')
const { JWT_SECRET, getClientIP } = require('../../middleware/auth')
const { createAdminLog } = require('../../middleware/adminLogger')

const router = express.Router()

/**
 * 管理员登录
 * POST /api/admin/auth/login
 */
router.post('/login', async (req, res) => {
  try {
    const { username, password } = req.body
    const clientIP = getClientIP(req)

    // 参数验证
    if (!username || !password) {
      return res.status(400).json({ 
        success: false,
        error: '请填写用户名和密码' 
      })
    }

    // 查找管理员
    const admin = await Admin.findOne({ where: { username } })
    if (!admin) {
      return res.status(401).json({ 
        success: false,
        error: '管理员账号或密码错误' 
      })
    }

    // 检查管理员状态
    if (admin.status === '禁用') {
      return res.status(401).json({ 
        success: false,
        error: '管理员账户已被禁用' 
      })
    }

    // 验证密码
    const isValidPassword = await bcrypt.compare(password, admin.passwordHash)
    if (!isValidPassword) {
      return res.status(401).json({ 
        success: false,
        error: '管理员账号或密码错误' 
      })
    }

    // 更新最后登录信息
    await admin.update({
      lastLoginAt: new Date(),
      lastLoginIp: clientIP
    })

    // 生成JWT令牌
    const token = jwt.sign(
      {
        adminId: admin.id,
        username: admin.username,
        role: admin.role,
        isAdmin: true
      },
      JWT_SECRET,
      { expiresIn: '8h' } // 管理员token有效期8小时
    )

    // 记录登录日志
    await createAdminLog(
      admin.id,
      '登录',
      `管理员 ${admin.username} 登录系统`,
      '低',
      {
        loginTime: new Date().toISOString(),
        loginMethod: 'password',
        clientIP: clientIP,
        userAgent: req.headers['user-agent']
      },
      req
    )

    res.json({
      success: true,
      message: '管理员登录成功',
      data: {
        token,
        admin: {
          id: admin.id,
          username: admin.username,
          email: admin.email,
          role: admin.role,
          status: admin.status,
          lastLoginAt: admin.lastLoginAt,
          lastLoginIp: admin.lastLoginIp
        }
      }
    })
  } catch (error) {
    console.error('管理员登录错误:', error)
    res.status(500).json({ 
      success: false,
      error: '登录失败，请稍后重试' 
    })
  }
})

/**
 * 管理员登出
 * POST /api/admin/auth/logout
 */
router.post('/logout', (req, res) => {
  // 前端删除token即可，后端无需特殊处理
  res.json({
    success: true,
    message: '登出成功'
  })
})

/**
 * 验证管理员token
 * GET /api/admin/auth/verify
 */
router.get('/verify', async (req, res) => {
  try {
    const authHeader = req.headers['authorization']
    const token = authHeader && authHeader.split(' ')[1]

    if (!token) {
      return res.status(401).json({ 
        success: false,
        error: '未提供token' 
      })
    }

    const decoded = jwt.verify(token, JWT_SECRET)
    
    // 验证是否为管理员token
    if (!decoded.isAdmin) {
      return res.status(403).json({ 
        success: false,
        error: '需要管理员权限' 
      })
    }

    // 查找管理员确认状态
    const admin = await Admin.findByPk(decoded.adminId)
    if (!admin || admin.status === '禁用') {
      return res.status(401).json({ 
        success: false,
        error: '管理员账户状态异常' 
      })
    }

    res.json({
      success: true,
      data: {
        admin: {
          id: admin.id,
          username: admin.username,
          email: admin.email,
          role: admin.role,
          status: admin.status,
          lastLoginAt: admin.lastLoginAt,
          lastLoginIp: admin.lastLoginIp
        }
      }
    })
  } catch (error) {
    console.error('管理员Token验证错误:', error)
    res.status(401).json({ 
      success: false,
      error: '无效的管理员token' 
    })
  }
})

/**
 * 修改管理员密码
 * PUT /api/admin/auth/change-password
 */
router.put('/change-password', async (req, res) => {
  try {
    const authHeader = req.headers['authorization']
    const token = authHeader && authHeader.split(' ')[1]

    if (!token) {
      return res.status(401).json({ 
        success: false,
        error: '需要登录' 
      })
    }

    const decoded = jwt.verify(token, JWT_SECRET)
    const { currentPassword, newPassword } = req.body

    // 参数验证
    if (!currentPassword || !newPassword) {
      return res.status(400).json({ 
        success: false,
        error: '请填写当前密码和新密码' 
      })
    }

    if (newPassword.length < 6) {
      return res.status(400).json({ 
        success: false,
        error: '新密码长度至少6位' 
      })
    }

    // 查找管理员
    const admin = await Admin.findByPk(decoded.adminId)
    if (!admin) {
      return res.status(404).json({ 
        success: false,
        error: '管理员不存在' 
      })
    }

    // 验证当前密码
    const isValidPassword = await bcrypt.compare(currentPassword, admin.passwordHash)
    if (!isValidPassword) {
      return res.status(401).json({ 
        success: false,
        error: '当前密码错误' 
      })
    }

    // 加密新密码
    const hashedNewPassword = await bcrypt.hash(newPassword, 10)

    // 更新密码
    await admin.update({
      passwordHash: hashedNewPassword
    })

    res.json({
      success: true,
      message: '密码修改成功'
    })
  } catch (error) {
    console.error('修改管理员密码错误:', error)
    res.status(500).json({ 
      success: false,
      error: '修改密码失败' 
    })
  }
})

module.exports = router
