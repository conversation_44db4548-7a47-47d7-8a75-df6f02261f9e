const { AdminLog } = require('../models')

/**
 * 管理员操作日志记录中间件
 */
const logAdminAction = (action, description, riskLevel = '低', details = null) => {
  return async (req, res, next) => {
    // 先执行原始操作
    const originalSend = res.send
    
    res.send = function(data) {
      // 记录日志
      recordAdminLog(req, action, description, riskLevel, details, data)
      
      // 调用原始的 send 方法
      originalSend.call(this, data)
    }
    
    next()
  }
}

/**
 * 记录管理员操作日志
 */
const recordAdminLog = async (req, action, description, riskLevel, details, responseData) => {
  try {
    // 获取管理员ID
    const adminId = req.admin?.id
    if (!adminId) {
      console.log('无法记录日志：未找到管理员ID')
      return
    }

    // 获取客户端信息
    const ipAddress = req.ip || req.connection.remoteAddress || req.headers['x-forwarded-for']
    const userAgent = req.headers['user-agent']

    // 构建详细信息
    let logDetails = details || {}
    
    // 如果是函数，则调用获取详细信息
    if (typeof details === 'function') {
      logDetails = details(req, responseData)
    }

    // 添加请求信息到详细信息
    logDetails = {
      ...logDetails,
      method: req.method,
      url: req.originalUrl,
      params: req.params,
      query: req.query,
      timestamp: new Date().toISOString()
    }

    // 创建日志记录
    await AdminLog.create({
      adminId,
      action,
      description,
      riskLevel,
      ipAddress,
      userAgent,
      details: logDetails
    })

    console.log(`✅ 管理员操作日志已记录: ${action} - ${description}`)
  } catch (error) {
    console.error('❌ 记录管理员操作日志失败:', error)
  }
}

/**
 * 直接记录日志的函数（用于不通过中间件的场景）
 */
const createAdminLog = async (adminId, action, description, riskLevel = '低', details = null, req = null) => {
  try {
    let ipAddress = null
    let userAgent = null

    if (req) {
      ipAddress = req.ip || req.connection.remoteAddress || req.headers['x-forwarded-for']
      userAgent = req.headers['user-agent']
    }

    await AdminLog.create({
      adminId,
      action,
      description,
      riskLevel,
      ipAddress,
      userAgent,
      details
    })

    console.log(`✅ 管理员操作日志已记录: ${action} - ${description}`)
  } catch (error) {
    console.error('❌ 记录管理员操作日志失败:', error)
  }
}

module.exports = {
  logAdminAction,
  recordAdminLog,
  createAdminLog
}
