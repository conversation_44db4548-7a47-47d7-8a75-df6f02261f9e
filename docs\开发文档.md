# Vue抽奖系统开发文档

## 📋 项目概述

这是一个基于Vue 3 + Node.js + MySQL的完整抽奖系统，包含用户前台和管理后台两个部分。

### 🎯 核心功能
- **用户前台**：用户注册登录、抽奖参与、记录查看、活动规则
- **管理后台**：用户管理、抽奖期次管理、记录查看、系统设置、操作日志

### 🛠 技术栈
- **前端**：Vue 3 + TypeScript + Vite + Tailwind CSS + Element Plus
- **后端**：Node.js + Express + Sequelize ORM
- **数据库**：MySQL 8.0.36+
- **认证**：JWT Token
- **密码加密**：bcrypt

---

## 🏗 项目结构

```
vue抽奖/
├── frontend/                 # 前端项目
│   ├── src/
│   │   ├── api/             # API接口配置
│   │   ├── components/      # 公共组件
│   │   ├── stores/          # Pinia状态管理
│   │   ├── views/           # 页面组件
│   │   │   ├── admin/       # 管理后台页面
│   │   │   └── user/        # 用户前台页面
│   │   ├── router/          # 路由配置
│   │   └── utils/           # 工具函数
│   ├── public/              # 静态资源
│   └── package.json
├── backend/                 # 后端项目
│   ├── src/
│   │   ├── config/          # 配置文件
│   │   ├── models/          # 数据模型
│   │   ├── routes/          # 路由处理
│   │   ├── middleware/      # 中间件
│   │   └── utils/           # 工具函数
│   └── package.json
├── database/                # 数据库脚本
│   ├── create_tables.sql    # 建表脚本
│   └── init_data.sql        # 初始数据
└── docs/                    # 文档
    └── 开发文档.md
```

---

## 🎨 前端架构

### 📱 用户前台页面

#### 1. 首页 (`/`)
- **文件**：`frontend/src/views/HomeView.vue`
- **功能**：平台介绍、快速导航、活动展示
- **组件**：
  - `AppHeader.vue` - 公共头部
  - `AppFooter.vue` - 公共底部

#### 2. 抽奖页面 (`/lottery`)
- **文件**：`frontend/src/views/LotteryView.vue`
- **功能**：抽奖转盘、抽奖逻辑、结果展示
- **核心组件**：
  - Lucky Canvas 抽奖轮盘
  - 响应式设计适配H5

#### 3. 记录页面 (`/records`)
- **文件**：`frontend/src/views/RecordsView.vue`
- **功能**：个人抽奖记录查看、分页展示
- **特性**：
  - 桌面端表格布局
  - 移动端卡片布局
  - 筛选和分页功能

#### 4. 规则页面 (`/rules`)
- **文件**：`frontend/src/views/RulesView.vue`
- **功能**：活动规则说明、奖品介绍

### 🔧 管理后台页面

#### 1. 登录页面 (`/admin/login`)
- **文件**：`frontend/src/views/admin/AdminLogin.vue`
- **功能**：管理员登录认证
- **特性**：深色主题、表单验证、安全防护

#### 2. 仪表板 (`/admin/dashboard`)
- **文件**：`frontend/src/views/admin/AdminDashboard.vue`
- **功能**：数据统计、快速操作、系统概览
- **组件**：
  - 统计卡片
  - 快速操作按钮
  - 最近活动时间线

#### 3. 用户管理 (`/admin/users`)
- **文件**：`frontend/src/views/admin/AdminUsers.vue`
- **功能**：用户信息管理、状态控制
- **特性**：
  - 卡片式布局
  - 搜索筛选
  - 用户详情查看

#### 4. 管理员管理 (`/admin/admins`)
- **文件**：`frontend/src/views/admin/AdminManagers.vue`
- **功能**：管理员账户管理
- **特性**：
  - 添加/编辑管理员
  - 权限控制
  - 状态管理

#### 5. 抽奖记录 (`/admin/lottery-records`)
- **文件**：`frontend/src/views/admin/AdminLotteryRecords.vue`
- **功能**：所有抽奖记录查看
- **特性**：
  - 表格布局
  - 搜索筛选
  - 记录详情

#### 6. 期次管理 (`/admin/lottery-periods`)
- **文件**：`frontend/src/views/admin/AdminLotteryPeriods.vue`
- **功能**：抽奖期次创建和管理
- **特性**：
  - 期次时间设置
  - 状态管理
  - 参与人数统计

#### 7. 用户操作日志 (`/admin/user-logs`)
- **文件**：`frontend/src/views/admin/AdminUserLogs.vue`
- **功能**：用户操作记录审计
- **特性**：
  - 操作类型分类
  - IP地址记录
  - 详细信息查看

#### 8. 管理员操作日志 (`/admin/admin-logs`)
- **文件**：`frontend/src/views/admin/AdminLogs.vue`
- **功能**：管理员操作记录审计
- **特性**：
  - 风险等级标识
  - 操作详情记录
  - 安全审计

#### 9. 系统设置 (`/admin/settings`)
- **文件**：`frontend/src/views/admin/AdminSettings.vue`
- **功能**：系统配置管理
- **模块**：
  - 奖品配置（金额、概率）
  - 系统参数（平台信息、客服信息）
  - 广告横幅管理
  - 数据管理（导出、清理、重置）

### 🎨 设计系统

#### 主题配色
- **用户前台**：深色主题 + 金色强调
  - 背景：`bg-gradient-to-br from-gray-900 via-black to-gray-800`
  - 主色：`#d4af37` (金色)
  - 辅助色：`#b8941f` (深金色)

- **管理后台**：深色主题 + 金色强调
  - 背景：`bg-gradient-to-br from-gray-900 via-black to-gray-800`
  - 卡片：`bg-gradient-to-br from-gray-800 to-gray-900`
  - 边框：`border-gold-500/30`

#### 响应式断点
- `sm`: 640px (手机横屏)
- `md`: 768px (平板)
- `lg`: 1024px (桌面)
- `xl`: 1280px (大屏)

---

## 🔧 后端架构

### 📁 目录结构

```
backend/src/
├── config/
│   └── database.js          # 数据库连接配置
├── models/                  # Sequelize数据模型
│   ├── index.js            # 模型关联配置
│   ├── User.js             # 用户模型
│   ├── Admin.js            # 管理员模型
│   ├── LotteryPeriod.js    # 抽奖期次模型
│   ├── PrizeConfig.js      # 奖品配置模型
│   ├── LotteryRecord.js    # 抽奖记录模型
│   └── UserLotteryLimit.js # 用户抽奖限制模型
├── routes/                  # 路由处理
│   ├── auth.js             # 用户认证路由
│   └── admin/
│       └── auth.js         # 管理员认证路由
├── middleware/
│   └── auth.js             # 认证中间件
├── utils/
│   └── database.js         # 数据库工具函数
└── app.js                  # 主应用文件
```

### 🗄 数据库设计

#### 1. 用户表 (users)
```sql
CREATE TABLE users (
  id INT PRIMARY KEY AUTO_INCREMENT,
  username VARCHAR(50) UNIQUE NOT NULL,
  password VARCHAR(255) NOT NULL,
  platformId VARCHAR(20) UNIQUE NOT NULL,
  balance DECIMAL(10,2) DEFAULT 0.00,
  status ENUM('active', 'disabled') DEFAULT 'active',
  lastLoginAt DATETIME,
  createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
  updatedAt DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

#### 2. 管理员表 (admins)
```sql
CREATE TABLE admins (
  id INT PRIMARY KEY AUTO_INCREMENT,
  username VARCHAR(50) UNIQUE NOT NULL,
  password VARCHAR(255) NOT NULL,
  status ENUM('active', 'disabled') DEFAULT 'active',
  lastLoginAt DATETIME,
  createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
  updatedAt DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

#### 3. 抽奖期次表 (lottery_periods)
```sql
CREATE TABLE lottery_periods (
  id INT PRIMARY KEY AUTO_INCREMENT,
  name VARCHAR(100) NOT NULL,
  startTime DATETIME NOT NULL,
  endTime DATETIME NOT NULL,
  status ENUM('pending', 'active', 'ended') DEFAULT 'pending',
  participantCount INT DEFAULT 0,
  createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
  updatedAt DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

#### 4. 奖品配置表 (prize_configs)
```sql
CREATE TABLE prize_configs (
  id INT PRIMARY KEY AUTO_INCREMENT,
  level INT NOT NULL,
  name VARCHAR(50) NOT NULL,
  amount DECIMAL(10,2) NOT NULL,
  probability DECIMAL(5,2) NOT NULL,
  isActive BOOLEAN DEFAULT true,
  createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
  updatedAt DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

#### 5. 抽奖记录表 (lottery_records)
```sql
CREATE TABLE lottery_records (
  id INT PRIMARY KEY AUTO_INCREMENT,
  userId INT NOT NULL,
  periodId INT NOT NULL,
  prizeLevel INT,
  prizeName VARCHAR(50),
  prizeAmount DECIMAL(10,2) DEFAULT 0.00,
  isWin BOOLEAN DEFAULT false,
  createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (userId) REFERENCES users(id),
  FOREIGN KEY (periodId) REFERENCES lottery_periods(id)
);
```

#### 6. 用户抽奖限制表 (user_lottery_limits)
```sql
CREATE TABLE user_lottery_limits (
  id INT PRIMARY KEY AUTO_INCREMENT,
  userId INT NOT NULL,
  periodId INT NOT NULL,
  maxAttempts INT DEFAULT 3,
  usedAttempts INT DEFAULT 0,
  createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
  updatedAt DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (userId) REFERENCES users(id),
  FOREIGN KEY (periodId) REFERENCES lottery_periods(id),
  UNIQUE KEY unique_user_period (userId, periodId)
);
```

### 🔐 API接口详细规范

#### 1. 用户认证接口

##### POST /api/auth/register - 用户注册
```javascript
// 请求体
{
  "username": "user001",
  "password": "123456"
}

// 响应
{
  "success": true,
  "message": "注册成功",
  "data": {
    "user": {
      "id": 1,
      "username": "user001",
      "platformId": "PID001",
      "balance": 0.00,
      "status": "active"
    },
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
  }
}
```

##### POST /api/auth/login - 用户登录
```javascript
// 请求体
{
  "username": "user001",
  "password": "123456"
}

// 响应
{
  "success": true,
  "message": "登录成功",
  "data": {
    "user": {
      "id": 1,
      "username": "user001",
      "platformId": "PID001",
      "balance": 0.00,
      "status": "active",
      "lastLoginAt": "2024-01-15T14:30:00.000Z"
    },
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
  }
}
```

##### GET /api/auth/verify - 验证Token
```javascript
// 请求头
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...

// 响应
{
  "success": true,
  "data": {
    "user": {
      "id": 1,
      "username": "user001",
      "platformId": "PID001",
      "balance": 0.00,
      "status": "active"
    }
  }
}
```

#### 2. 管理员认证接口

##### POST /api/admin/auth/login - 管理员登录
```javascript
// 请求体
{
  "username": "admin001",
  "password": "admin123"
}

// 响应
{
  "success": true,
  "message": "登录成功",
  "data": {
    "admin": {
      "id": 1,
      "username": "admin001",
      "status": "active",
      "lastLoginAt": "2024-01-15T14:30:00.000Z"
    },
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
  }
}
```

##### POST /api/admin/auth/logout - 管理员登出
```javascript
// 请求头
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...

// 响应
{
  "success": true,
  "message": "登出成功"
}
```

##### GET /api/admin/auth/verify - 验证管理员Token
```javascript
// 请求头
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...

// 响应
{
  "success": true,
  "data": {
    "admin": {
      "id": 1,
      "username": "admin001",
      "status": "active"
    }
  }
}
```

##### POST /api/admin/auth/change-password - 修改密码
```javascript
// 请求体
{
  "oldPassword": "admin123",
  "newPassword": "newpassword123"
}

// 响应
{
  "success": true,
  "message": "密码修改成功"
}
```

#### 3. 抽奖相关接口

##### GET /api/lottery/current-period - 获取当前期次
```javascript
// 响应
{
  "success": true,
  "data": {
    "period": {
      "id": 1,
      "name": "2024年第1期",
      "startTime": "2024-01-15T00:00:00.000Z",
      "endTime": "2024-01-20T23:59:59.000Z",
      "status": "active",
      "participantCount": 156
    }
  }
}
```

##### POST /api/lottery/draw - 执行抽奖
```javascript
// 请求头
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...

// 请求体
{
  "periodId": 1
}

// 响应
{
  "success": true,
  "message": "抽奖成功",
  "data": {
    "record": {
      "id": 123,
      "userId": 1,
      "periodId": 1,
      "prizeLevel": 5,
      "prizeName": "四等奖",
      "prizeAmount": 100.00,
      "isWin": true,
      "createdAt": "2024-01-15T14:30:00.000Z"
    },
    "remainingAttempts": 2
  }
}
```

##### GET /api/lottery/records - 获取抽奖记录
```javascript
// 请求头
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...

// 查询参数
?page=1&limit=10&periodId=1

// 响应
{
  "success": true,
  "data": {
    "records": [
      {
        "id": 123,
        "periodId": 1,
        "periodName": "2024年第1期",
        "prizeLevel": 5,
        "prizeName": "四等奖",
        "prizeAmount": 100.00,
        "isWin": true,
        "createdAt": "2024-01-15T14:30:00.000Z"
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 10,
      "total": 25,
      "totalPages": 3
    }
  }
}
```

##### GET /api/lottery/user-limit - 获取用户抽奖限制
```javascript
// 请求头
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...

// 查询参数
?periodId=1

// 响应
{
  "success": true,
  "data": {
    "limit": {
      "periodId": 1,
      "maxAttempts": 3,
      "usedAttempts": 1,
      "remainingAttempts": 2
    }
  }
}
```

#### 4. 管理后台接口

##### GET /api/admin/users - 获取用户列表
```javascript
// 请求头
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...

// 查询参数
?page=1&limit=20&search=user&status=active

// 响应
{
  "success": true,
  "data": {
    "users": [
      {
        "id": 1,
        "username": "user001",
        "platformId": "PID001",
        "balance": 150.00,
        "status": "active",
        "lastLoginAt": "2024-01-15T14:30:00.000Z",
        "createdAt": "2024-01-10T10:00:00.000Z",
        "lotteryCount": 5,
        "winCount": 2,
        "totalWinAmount": 300.00
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 20,
      "total": 156,
      "totalPages": 8
    }
  }
}
```

##### PUT /api/admin/users/:id - 更新用户信息
```javascript
// 请求头
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...

// 请求体
{
  "status": "disabled",
  "balance": 200.00
}

// 响应
{
  "success": true,
  "message": "用户信息更新成功",
  "data": {
    "user": {
      "id": 1,
      "username": "user001",
      "platformId": "PID001",
      "balance": 200.00,
      "status": "disabled",
      "updatedAt": "2024-01-15T14:30:00.000Z"
    }
  }
}
```

##### GET /api/admin/lottery-records - 获取所有抽奖记录
```javascript
// 请求头
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...

// 查询参数
?page=1&limit=20&periodId=1&isWin=true&startDate=2024-01-01&endDate=2024-01-31

// 响应
{
  "success": true,
  "data": {
    "records": [
      {
        "id": 123,
        "user": {
          "id": 1,
          "username": "user001",
          "platformId": "PID001"
        },
        "period": {
          "id": 1,
          "name": "2024年第1期"
        },
        "prizeLevel": 5,
        "prizeName": "四等奖",
        "prizeAmount": 100.00,
        "isWin": true,
        "createdAt": "2024-01-15T14:30:00.000Z"
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 20,
      "total": 1250,
      "totalPages": 63
    },
    "statistics": {
      "totalRecords": 1250,
      "winRecords": 312,
      "winRate": 24.96,
      "totalPayout": 45600.00
    }
  }
}
```

##### GET /api/admin/lottery-periods - 获取期次列表
```javascript
// 请求头
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...

// 查询参数
?page=1&limit=10&status=active

// 响应
{
  "success": true,
  "data": {
    "periods": [
      {
        "id": 1,
        "name": "2024年第1期",
        "startTime": "2024-01-15T00:00:00.000Z",
        "endTime": "2024-01-20T23:59:59.000Z",
        "status": "active",
        "participantCount": 156,
        "totalRecords": 468,
        "totalPayout": 12500.00,
        "createdAt": "2024-01-10T10:00:00.000Z"
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 10,
      "total": 5,
      "totalPages": 1
    }
  }
}
```

##### POST /api/admin/lottery-periods - 创建新期次
```javascript
// 请求头
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...

// 请求体
{
  "name": "2024年第2期",
  "startTime": "2024-01-21T00:00:00.000Z",
  "endTime": "2024-01-25T23:59:59.000Z"
}

// 响应
{
  "success": true,
  "message": "期次创建成功",
  "data": {
    "period": {
      "id": 2,
      "name": "2024年第2期",
      "startTime": "2024-01-21T00:00:00.000Z",
      "endTime": "2024-01-25T23:59:59.000Z",
      "status": "pending",
      "participantCount": 0,
      "createdAt": "2024-01-15T14:30:00.000Z"
    }
  }
}
```

##### GET /api/admin/settings - 获取系统设置
```javascript
// 请求头
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...

// 响应
{
  "success": true,
  "data": {
    "systemSettings": {
      "platformName": "抽奖平台",
      "customerServiceQQ": "123456789",
      "customerServiceWechat": "service_wechat",
      "defaultMaxAttempts": 3,
      "maintenanceMode": false,
      "maintenanceNotice": "系统维护中，请稍后再试..."
    },
    "prizeConfig": [
      {
        "id": 1,
        "level": 1,
        "name": "特等奖",
        "amount": 5000.00,
        "probability": 0.1,
        "isActive": true
      }
    ],
    "banners": [
      {
        "id": 1,
        "title": "首充优惠",
        "url": "https://example.com/first-deposit",
        "imageUrl": "https://example.com/banner1.jpg",
        "enabled": true,
        "sort": 1
      }
    ]
  }
}
```

##### PUT /api/admin/settings - 更新系统设置
```javascript
// 请求头
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...

// 请求体
{
  "systemSettings": {
    "platformName": "新抽奖平台",
    "customerServiceQQ": "987654321",
    "customerServiceWechat": "new_service_wechat",
    "defaultMaxAttempts": 5,
    "maintenanceMode": false,
    "maintenanceNotice": "系统升级中..."
  },
  "prizeConfig": [
    {
      "level": 1,
      "name": "特等奖",
      "amount": 8000.00,
      "probability": 0.05,
      "isActive": true
    }
  ]
}

// 响应
{
  "success": true,
  "message": "系统设置更新成功"
}
```

#### 5. 操作日志接口

##### GET /api/admin/user-logs - 获取用户操作日志
```javascript
// 请求头
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...

// 查询参数
?page=1&limit=20&action=登录&startDate=2024-01-01&endDate=2024-01-31

// 响应
{
  "success": true,
  "data": {
    "logs": [
      {
        "id": 1,
        "user": {
          "id": 1,
          "username": "user001",
          "platformId": "PID001"
        },
        "action": "登录",
        "description": "用户成功登录系统",
        "ipAddress": "*************",
        "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64)",
        "details": {
          "loginMethod": "password",
          "success": true
        },
        "createdAt": "2024-01-15T14:30:00.000Z"
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 20,
      "total": 2500,
      "totalPages": 125
    }
  }
}
```

##### GET /api/admin/admin-logs - 获取管理员操作日志
```javascript
// 请求头
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...

// 查询参数
?page=1&limit=20&action=用户管理&riskLevel=高

// 响应
{
  "success": true,
  "data": {
    "logs": [
      {
        "id": 1,
        "admin": {
          "id": 1,
          "username": "admin001"
        },
        "action": "用户管理",
        "description": "禁用用户账户 PID002",
        "riskLevel": "高",
        "ipAddress": "************",
        "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64)",
        "details": {
          "targetUserId": "PID002",
          "action": "disable",
          "reason": "违规操作"
        },
        "createdAt": "2024-01-15T14:30:00.000Z"
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 20,
      "total": 156,
      "totalPages": 8
    }
  }
}
```

#### 6. 错误处理规范

##### HTTP状态码
- `200` - 请求成功
- `201` - 创建成功
- `400` - 请求参数错误
- `401` - 未授权/Token无效
- `403` - 权限不足
- `404` - 资源不存在
- `409` - 数据冲突（如用户名已存在）
- `422` - 数据验证失败
- `500` - 服务器内部错误

##### 错误响应格式
```javascript
{
  "success": false,
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "数据验证失败",
    "details": {
      "username": ["用户名不能为空"],
      "password": ["密码长度至少6位"]
    }
  }
}
```

##### 常见错误代码
- `VALIDATION_ERROR` - 数据验证错误
- `AUTHENTICATION_ERROR` - 认证失败
- `AUTHORIZATION_ERROR` - 权限不足
- `RESOURCE_NOT_FOUND` - 资源不存在
- `DUPLICATE_RESOURCE` - 资源重复
- `BUSINESS_ERROR` - 业务逻辑错误
- `SYSTEM_ERROR` - 系统错误

#### 7. 数据验证规则

##### 用户注册验证
```javascript
{
  "username": {
    "required": true,
    "minLength": 3,
    "maxLength": 20,
    "pattern": "^[a-zA-Z0-9_]+$",
    "message": "用户名只能包含字母、数字和下划线"
  },
  "password": {
    "required": true,
    "minLength": 6,
    "maxLength": 20,
    "message": "密码长度必须在6-20位之间"
  }
}
```

##### 抽奖期次验证
```javascript
{
  "name": {
    "required": true,
    "maxLength": 100,
    "message": "期次名称不能超过100个字符"
  },
  "startTime": {
    "required": true,
    "type": "datetime",
    "message": "开始时间格式不正确"
  },
  "endTime": {
    "required": true,
    "type": "datetime",
    "after": "startTime",
    "message": "结束时间必须晚于开始时间"
  }
}
```

#### 8. 认证和授权

##### JWT Token结构
```javascript
// Header
{
  "alg": "HS256",
  "typ": "JWT"
}

// Payload
{
  "userId": 1,
  "username": "user001",
  "type": "user", // user | admin
  "iat": 1642248000,
  "exp": 1642334400
}
```

##### 权限控制
- **用户权限**：只能访问自己的数据和公共接口
- **管理员权限**：可以访问所有管理后台接口
- **Token过期时间**：用户Token 24小时，管理员Token 8小时

---

## 🚀 部署指南

### 开发环境启动

#### 1. 前端启动
```bash
cd frontend
npm install
npm run dev
# 访问 http://localhost:5173
```

#### 2. 后端启动
```bash
cd backend
npm install
npm run dev
# 服务运行在 http://localhost:3000
```

#### 3. 数据库初始化
```bash
# 使用Navicat或其他MySQL客户端
# 执行 database/create_tables.sql 创建表结构
# 执行 database/init_data.sql 插入初始数据
```

### 生产环境部署

#### 1. 前端构建
```bash
cd frontend
npm run build
# 将 dist/ 目录部署到Web服务器
```

#### 2. 后端部署
```bash
cd backend
npm install --production
npm start
# 使用PM2管理进程
pm2 start src/app.js --name "lottery-backend"
```

#### 3. 环境变量配置
```bash
# backend/.env
NODE_ENV=production
PORT=3000

# 数据库配置
DB_HOST=localhost
DB_PORT=3306
DB_NAME=lottery_system
DB_USER=root
DB_PASS=your_password

# JWT配置
JWT_SECRET=your_jwt_secret_key
JWT_EXPIRES_IN=24h
ADMIN_JWT_EXPIRES_IN=8h

# CORS配置
FRONTEND_URL=https://your-domain.com

# 日志配置
LOG_LEVEL=info
LOG_FILE=logs/app.log

# Redis配置（可选，用于缓存）
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=

# 邮件配置（可选）
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password
```

#### 4. 数据库配置
```javascript
// backend/src/config/database.js
module.exports = {
  host: process.env.DB_HOST || 'localhost',
  port: process.env.DB_PORT || 3306,
  database: process.env.DB_NAME || 'lottery_system',
  username: process.env.DB_USER || 'root',
  password: process.env.DB_PASS || '',
  dialect: 'mysql',
  timezone: '+08:00',
  pool: {
    max: 10,
    min: 0,
    acquire: 30000,
    idle: 10000
  },
  logging: process.env.NODE_ENV === 'development' ? console.log : false
}
```

#### 5. Nginx配置示例
```nginx
server {
    listen 80;
    server_name your-domain.com;

    # 前端静态文件
    location / {
        root /var/www/lottery-frontend/dist;
        try_files $uri $uri/ /index.html;

        # 缓存静态资源
        location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
            expires 1y;
            add_header Cache-Control "public, immutable";
        }
    }

    # 后端API代理
    location /api/ {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }
}
```

---

## 🔧 开发规范

### 代码规范
- 使用TypeScript进行类型检查
- 遵循ESLint配置规则
- 使用Prettier格式化代码
- 组件命名采用PascalCase
- 文件命名采用kebab-case

### Git提交规范
```
feat: 新功能
fix: 修复bug
docs: 文档更新
style: 代码格式调整
refactor: 代码重构
test: 测试相关
chore: 构建过程或辅助工具的变动
```

### 安全规范
- 所有API接口需要认证
- 密码使用bcrypt加密
- JWT Token设置合理过期时间
- 输入数据进行验证和过滤
- 敏感操作记录日志

---

## 📝 待开发功能

### 后端API开发清单
1. **用户管理API**
   - [ ] 用户注册接口
   - [ ] 用户登录接口
   - [ ] 用户信息更新接口
   - [ ] 用户状态管理接口

2. **抽奖系统API**
   - [ ] 抽奖执行接口
   - [ ] 抽奖记录查询接口
   - [ ] 用户抽奖次数限制接口
   - [ ] 奖品概率配置接口

3. **管理后台API**
   - [ ] 管理员认证接口
   - [ ] 用户管理接口
   - [ ] 期次管理接口
   - [ ] 系统设置接口
   - [ ] 操作日志接口

4. **数据统计API**
   - [ ] 用户统计接口
   - [ ] 抽奖统计接口
   - [ ] 收益统计接口
   - [ ] 数据导出接口

### 功能增强
- [ ] 短信验证码登录
- [ ] 邮箱通知功能
- [ ] 实时消息推送
- [ ] 数据可视化图表
- [ ] 移动端APP适配
- [ ] 多语言支持

---

## ⚡ 性能优化建议

### 前端优化
1. **代码分割**：使用Vue Router的懒加载
2. **资源压缩**：启用Gzip压缩
3. **图片优化**：使用WebP格式，添加懒加载
4. **缓存策略**：合理设置静态资源缓存
5. **CDN加速**：静态资源使用CDN分发

### 后端优化
1. **数据库索引**：为常用查询字段添加索引
2. **连接池**：配置合适的数据库连接池
3. **缓存机制**：使用Redis缓存热点数据
4. **API限流**：防止恶意请求
5. **日志优化**：异步写入日志文件

### 数据库优化
```sql
-- 添加索引
CREATE INDEX idx_users_username ON users(username);
CREATE INDEX idx_users_platform_id ON users(platformId);
CREATE INDEX idx_lottery_records_user_id ON lottery_records(userId);
CREATE INDEX idx_lottery_records_period_id ON lottery_records(periodId);
CREATE INDEX idx_lottery_records_created_at ON lottery_records(createdAt);

-- 分区表（大数据量时）
ALTER TABLE lottery_records PARTITION BY RANGE (YEAR(createdAt)) (
    PARTITION p2024 VALUES LESS THAN (2025),
    PARTITION p2025 VALUES LESS THAN (2026),
    PARTITION p_future VALUES LESS THAN MAXVALUE
);
```

---

## 📊 监控和日志

### 应用监控
1. **性能监控**：响应时间、吞吐量、错误率
2. **资源监控**：CPU、内存、磁盘使用率
3. **业务监控**：用户活跃度、抽奖频率、中奖率

### 日志管理
```javascript
// backend/src/utils/logger.js
const winston = require('winston');

const logger = winston.createLogger({
  level: process.env.LOG_LEVEL || 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.errors({ stack: true }),
    winston.format.json()
  ),
  transports: [
    new winston.transports.File({ filename: 'logs/error.log', level: 'error' }),
    new winston.transports.File({ filename: 'logs/combined.log' }),
    new winston.transports.Console({
      format: winston.format.simple()
    })
  ]
});

module.exports = logger;
```

### 错误追踪
```javascript
// 全局错误处理
process.on('uncaughtException', (error) => {
  logger.error('Uncaught Exception:', error);
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  logger.error('Unhandled Rejection at:', promise, 'reason:', reason);
});
```

---

## 🔒 安全建议

### 数据安全
1. **密码加密**：使用bcrypt加密存储
2. **SQL注入防护**：使用参数化查询
3. **XSS防护**：输入输出过滤
4. **CSRF防护**：使用CSRF Token
5. **敏感数据加密**：重要信息加密存储

### 接口安全
1. **请求限流**：防止暴力破解
2. **参数验证**：严格验证输入参数
3. **权限控制**：细粒度权限管理
4. **日志审计**：记录敏感操作
5. **HTTPS**：强制使用HTTPS

### 示例安全中间件
```javascript
// backend/src/middleware/security.js
const rateLimit = require('express-rate-limit');
const helmet = require('helmet');

// 请求限流
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15分钟
  max: 100, // 最多100次请求
  message: '请求过于频繁，请稍后再试'
});

// 安全头设置
app.use(helmet());
app.use('/api/', limiter);
```

---

## 🧪 测试策略

### 单元测试
```javascript
// 使用Jest进行单元测试
// backend/tests/auth.test.js
const request = require('supertest');
const app = require('../src/app');

describe('Auth API', () => {
  test('POST /api/auth/login', async () => {
    const response = await request(app)
      .post('/api/auth/login')
      .send({
        username: 'testuser',
        password: 'testpass'
      });

    expect(response.status).toBe(200);
    expect(response.body.success).toBe(true);
    expect(response.body.data.token).toBeDefined();
  });
});
```

### 集成测试
```javascript
// frontend/tests/e2e/lottery.spec.js
// 使用Cypress进行E2E测试
describe('Lottery System', () => {
  it('should allow user to participate in lottery', () => {
    cy.visit('/login');
    cy.get('[data-cy=username]').type('testuser');
    cy.get('[data-cy=password]').type('testpass');
    cy.get('[data-cy=login-btn]').click();

    cy.visit('/lottery');
    cy.get('[data-cy=spin-btn]').click();
    cy.get('[data-cy=result-modal]').should('be.visible');
  });
});
```

---

## 📞 技术支持

如有技术问题，请参考：
1. Vue 3 官方文档：https://vuejs.org/
2. Element Plus 组件库：https://element-plus.org/
3. Tailwind CSS 文档：https://tailwindcss.com/
4. Sequelize ORM 文档：https://sequelize.org/
5. Express.js 文档：https://expressjs.com/

---

## 🎉 项目完成状态

### ✅ 已完成功能

#### 后端API开发
- [x] **数据库模型完善**：Sequelize模型与数据库表结构完全一致
- [x] **用户认证API**：注册、登录、Token验证，密码加密，JWT生成
- [x] **抽奖系统API**：获取当前期次、执行抽奖、查询记录、用户限制
- [x] **管理员认证API**：管理员登录、权限验证、密码修改
- [x] **管理后台API**：用户管理、期次管理、记录查看、系统设置

#### 前端集成
- [x] **API配置更新**：前端API调用与后端接口完全匹配
- [x] **认证状态管理**：用户和管理员认证状态分离管理
- [x] **数据格式处理**：请求响应数据格式统一处理

#### 功能测试
- [x] **后端服务启动**：服务器正常启动，数据库连接成功
- [x] **API接口测试**：用户注册、登录、管理员登录等核心接口正常
- [x] **前端服务启动**：前端开发服务器正常启动
- [x] **前后端集成**：前后端服务正常通信

### 🚀 部署说明

#### 环境要求
- Node.js 16.0+
- MySQL 8.0.36+
- npm 或 yarn

#### 后端部署步骤
1. **安装依赖**
   ```bash
   cd backend
   npm install
   ```

2. **环境配置**
   ```bash
   # 创建 .env 文件
   cp .env.example .env
   # 配置数据库连接信息
   ```

3. **启动服务**
   ```bash
   # 开发环境
   npm run dev

   # 生产环境
   npm start
   ```

4. **服务验证**
   - 健康检查：http://localhost:3000/health
   - API文档：http://localhost:3000/api

#### 前端部署步骤
1. **安装依赖**
   ```bash
   cd frontend
   npm install
   ```

2. **开发环境启动**
   ```bash
   npm run dev
   ```

3. **生产环境构建**
   ```bash
   npm run build
   ```

4. **访问地址**
   - 开发环境：http://localhost:5174
   - 用户前台：http://localhost:5174/
   - 管理后台：http://localhost:5174/admin

#### 数据库初始化
系统首次启动时会自动：
- 创建数据库表结构
- 初始化管理员账户（admin/admin123）
- 创建测试用户数据
- 创建示例抽奖期次

### 🔧 API接口变更记录

#### 新增接口
- `GET /api/lottery/current-period` - 获取当前活动期次
- `GET /api/lottery/user-limit` - 获取用户抽奖限制
- `POST /api/lottery/draw` - 执行抽奖
- `GET /api/lottery/records` - 获取用户抽奖记录
- `GET /api/lottery/user-status` - 获取用户状态信息
- `GET /api/admin/dashboard/stats` - 获取仪表板统计
- `GET /api/admin/users` - 获取用户列表
- `PUT /api/admin/users/:id` - 更新用户信息
- `GET /api/admin/lottery-records` - 获取抽奖记录
- `GET /api/admin/lottery-periods` - 获取期次列表
- `POST /api/admin/lottery-periods` - 创建新期次
- `PUT /api/admin/lottery-periods/:id/status` - 更新期次状态
- `GET /api/admin/settings` - 获取系统设置
- `PUT /api/admin/settings` - 更新系统设置
- `GET /api/admin/admins` - 获取管理员列表
- `POST /api/admin/admins` - 创建管理员
- `PUT /api/admin/admins/:id` - 更新管理员信息

#### 接口格式统一
- 所有接口返回格式：`{ success: boolean, message?: string, data?: any, error?: string }`
- 认证方式：Bearer Token
- 分页格式：`{ page, limit, total, totalPages }`

### 📝 开发注意事项

1. **数据库连接**：确保MySQL服务正常运行，数据库配置正确
2. **端口冲突**：后端默认3000端口，前端默认5174端口
3. **CORS配置**：已配置跨域支持，支持前后端分离部署
4. **Token管理**：用户Token和管理员Token分别管理，有效期不同
5. **错误处理**：统一错误处理机制，前端自动处理401状态码

### 🎯 后续优化建议

1. **性能优化**
   - 添加Redis缓存
   - 数据库查询优化
   - 前端代码分割

2. **功能扩展**
   - 短信验证码
   - 邮件通知
   - 数据导出功能

3. **安全加固**
   - API限流
   - 输入验证增强
   - 日志审计

---

*文档版本：v2.0*
*最后更新：2025-07-27*
*开发状态：✅ 完成*
