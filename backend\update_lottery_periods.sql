-- 更新lottery_periods表结构
-- 添加三个新字段：本期抽奖总金额、实际可抽取金额、总份数

USE as_chou;

-- 检查并添加本期抽奖总金额字段
SET @sql = (SELECT IF(
  (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
   WHERE TABLE_SCHEMA = 'as_chou'
   AND TABLE_NAME = 'lottery_periods'
   AND COLUMN_NAME = 'total_prize_amount') = 0,
  'ALTER TABLE lottery_periods ADD COLUMN total_prize_amount DECIMAL(15,2) DEFAULT 0.00 COMMENT ''本期抽奖总金额'' AFTER total_attempts;',
  'SELECT ''total_prize_amount字段已存在'' AS message;'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 检查并添加实际可抽取金额字段
SET @sql = (SELECT IF(
  (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
   WHERE TABLE_SCHEMA = 'as_chou'
   AND TABLE_NAME = 'lottery_periods'
   AND COLUMN_NAME = 'actual_drawable_amount') = 0,
  'ALTER TABLE lottery_periods ADD COLUMN actual_drawable_amount DECIMAL(15,2) DEFAULT 0.00 COMMENT ''实际可抽取金额'' AFTER total_prize_amount;',
  'SELECT ''actual_drawable_amount字段已存在'' AS message;'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 检查并添加总份数字段
SET @sql = (SELECT IF(
  (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
   WHERE TABLE_SCHEMA = 'as_chou'
   AND TABLE_NAME = 'lottery_periods'
   AND COLUMN_NAME = 'total_shares') = 0,
  'ALTER TABLE lottery_periods ADD COLUMN total_shares INT DEFAULT 0 COMMENT ''总份数'' AFTER actual_drawable_amount;',
  'SELECT ''total_shares字段已存在'' AS message;'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 更新现有记录的默认值
UPDATE lottery_periods 
SET 
  total_prize_amount = COALESCE(total_prize_amount, 0.00),
  actual_drawable_amount = COALESCE(actual_drawable_amount, 0.00),
  total_shares = COALESCE(total_shares, 0)
WHERE 
  total_prize_amount IS NULL 
  OR actual_drawable_amount IS NULL 
  OR total_shares IS NULL;

-- 查看更新后的表结构
DESCRIBE lottery_periods;

-- 查看现有数据
SELECT 
  id,
  period_name,
  period_number,
  status,
  total_prize_amount,
  actual_drawable_amount,
  total_shares,
  max_attempts_per_user
FROM lottery_periods 
ORDER BY id DESC 
LIMIT 5;
