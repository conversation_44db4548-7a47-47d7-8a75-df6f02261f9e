-- =====================================================
-- 用户个人抽奖率配置系统 - JSON格式迁移脚本
-- 创建时间: 2025-01-28
-- 说明: 将用户抽奖率配置改为JSON格式存储，减少数据量
-- =====================================================

-- 1. 备份现有数据（如果存在）
DROP TABLE IF EXISTS `user_prize_configs_backup`;
CREATE TABLE `user_prize_configs_backup` AS 
SELECT * FROM `user_prize_configs` WHERE 1=1;

-- 2. 删除现有的user_prize_configs表
DROP TABLE IF EXISTS `user_prize_configs`;

-- 3. 创建新的JSON格式的用户抽奖率配置表
CREATE TABLE `user_prize_configs` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `prize_configs` json NOT NULL COMMENT '奖品配置JSON数据',
  `total_probability` decimal(5,4) NOT NULL DEFAULT '1.0000' COMMENT '总概率(应该等于1.0000)',
  `is_enabled` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否启用个人设置(0:使用全局设置, 1:使用个人设置)',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_user_id` (`user_id`) COMMENT '用户ID唯一索引',
  KEY `idx_enabled` (`is_enabled`) COMMENT '启用状态索引',
  KEY `idx_total_probability` (`total_probability`) COMMENT '总概率索引',
  CONSTRAINT `fk_user_prize_configs_user_id` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='用户个人抽奖率配置表(JSON格式)';

-- 4. 为现有用户创建默认的JSON格式配置
-- 获取当前的奖品配置并为每个用户创建默认设置
INSERT INTO `user_prize_configs` (`user_id`, `prize_configs`, `total_probability`, `is_enabled`)
SELECT 
    u.id as user_id,
    JSON_OBJECT(
        'configs', JSON_ARRAYAGG(
            JSON_OBJECT(
                'prizeConfigId', pc.id,
                'prizeName', pc.prize_name,
                'prizeAmount', pc.prize_amount,
                'globalProbability', pc.probability,
                'customProbability', pc.probability,
                'isEnabled', true
            )
        )
    ) as prize_configs,
    (SELECT SUM(probability) FROM prize_configs) as total_probability,
    0 as is_enabled
FROM `users` u
CROSS JOIN (SELECT 1) dummy
WHERE NOT EXISTS (
    SELECT 1 FROM `user_prize_configs` upc 
    WHERE upc.user_id = u.id
)
GROUP BY u.id;

-- 5. 创建触发器：当新用户注册时自动创建JSON格式的个人抽奖率配置
DROP TRIGGER IF EXISTS `tr_users_after_insert_json`;

DELIMITER $$

CREATE TRIGGER `tr_users_after_insert_json`
AFTER INSERT ON `users`
FOR EACH ROW
BEGIN
    DECLARE total_prob DECIMAL(5,4) DEFAULT 1.0000;
    
    -- 计算当前奖品配置的总概率
    SELECT COALESCE(SUM(probability), 1.0000) INTO total_prob FROM prize_configs;
    
    -- 为新用户创建默认的JSON格式抽奖率配置
    INSERT INTO `user_prize_configs` (`user_id`, `prize_configs`, `total_probability`, `is_enabled`)
    SELECT 
        NEW.id as user_id,
        JSON_OBJECT(
            'configs', JSON_ARRAYAGG(
                JSON_OBJECT(
                    'prizeConfigId', pc.id,
                    'prizeName', pc.prize_name,
                    'prizeAmount', pc.prize_amount,
                    'globalProbability', pc.probability,
                    'customProbability', pc.probability,
                    'isEnabled', true
                )
            )
        ) as prize_configs,
        total_prob as total_probability,
        0 as is_enabled
    FROM `prize_configs` pc
    WHERE EXISTS (SELECT 1 FROM prize_configs LIMIT 1);
END$$

DELIMITER ;

-- 6. 创建触发器：当新奖品配置添加时，为所有用户更新配置
DROP TRIGGER IF EXISTS `tr_prize_configs_after_insert_json`;

DELIMITER $$

CREATE TRIGGER `tr_prize_configs_after_insert_json`
AFTER INSERT ON `prize_configs`
FOR EACH ROW
BEGIN
    DECLARE done INT DEFAULT FALSE;
    DECLARE user_id_var BIGINT;
    DECLARE current_configs JSON;
    DECLARE new_configs JSON;
    DECLARE total_prob DECIMAL(5,4);
    
    -- 声明游标
    DECLARE user_cursor CURSOR FOR 
        SELECT user_id, prize_configs FROM user_prize_configs;
    DECLARE CONTINUE HANDLER FOR NOT FOUND SET done = TRUE;
    
    -- 计算新的总概率
    SELECT SUM(probability) INTO total_prob FROM prize_configs;
    
    -- 打开游标
    OPEN user_cursor;
    
    read_loop: LOOP
        FETCH user_cursor INTO user_id_var, current_configs;
        IF done THEN
            LEAVE read_loop;
        END IF;
        
        -- 为当前用户添加新奖品配置
        SET new_configs = JSON_ARRAY_APPEND(
            JSON_EXTRACT(current_configs, '$.configs'),
            '$',
            JSON_OBJECT(
                'prizeConfigId', NEW.id,
                'prizeName', NEW.prize_name,
                'prizeAmount', NEW.prize_amount,
                'globalProbability', NEW.probability,
                'customProbability', NEW.probability,
                'isEnabled', true
            )
        );
        
        -- 更新用户配置
        UPDATE user_prize_configs 
        SET 
            prize_configs = JSON_OBJECT('configs', new_configs),
            total_probability = total_prob,
            updated_at = CURRENT_TIMESTAMP
        WHERE user_id = user_id_var;
        
    END LOOP;
    
    CLOSE user_cursor;
END$$

DELIMITER ;

-- 7. 创建存储过程：一键设置用户不中奖（JSON版本）
DROP PROCEDURE IF EXISTS `sp_set_user_no_win_json`;

DELIMITER $$

CREATE PROCEDURE `sp_set_user_no_win_json`(
    IN p_user_id BIGINT,
    IN p_admin_id BIGINT
)
BEGIN
    DECLARE v_configs JSON;
    DECLARE v_new_configs JSON DEFAULT JSON_ARRAY();
    DECLARE v_config JSON;
    DECLARE v_count INT DEFAULT 0;
    DECLARE v_index INT DEFAULT 0;
    DECLARE v_prize_name VARCHAR(255);
    
    -- 获取用户当前配置
    SELECT prize_configs INTO v_configs 
    FROM user_prize_configs 
    WHERE user_id = p_user_id;
    
    -- 获取配置数组长度
    SET v_count = JSON_LENGTH(JSON_EXTRACT(v_configs, '$.configs'));
    
    -- 遍历所有配置，将非"谢谢参与"的奖品概率设为0
    WHILE v_index < v_count DO
        SET v_config = JSON_EXTRACT(v_configs, CONCAT('$.configs[', v_index, ']'));
        SET v_prize_name = JSON_UNQUOTE(JSON_EXTRACT(v_config, '$.prizeName'));
        
        -- 如果是"谢谢参与"，保持原概率；否则设为0
        IF v_prize_name LIKE '%谢谢参与%' OR v_prize_name LIKE '%谢谢%' THEN
            SET v_new_configs = JSON_ARRAY_APPEND(v_new_configs, '$', v_config);
        ELSE
            SET v_config = JSON_SET(v_config, '$.customProbability', 0.0000);
            SET v_new_configs = JSON_ARRAY_APPEND(v_new_configs, '$', v_config);
        END IF;
        
        SET v_index = v_index + 1;
    END WHILE;
    
    -- 更新用户配置
    UPDATE user_prize_configs 
    SET 
        prize_configs = JSON_OBJECT('configs', v_new_configs),
        total_probability = 1.0000,
        is_enabled = 1,
        updated_at = CURRENT_TIMESTAMP
    WHERE user_id = p_user_id;
    
    -- 记录操作日志
    INSERT INTO admin_logs (admin_id, action_type, action_description, risk_level, details, ip_address, user_agent, created_at)
    VALUES (
        p_admin_id,
        '用户抽奖设置',
        CONCAT('一键设置用户ID:', p_user_id, '为不中奖状态'),
        '中',
        JSON_OBJECT('userId', p_user_id, 'action', 'set_no_win', 'timestamp', NOW()),
        '127.0.0.1',
        'System',
        NOW()
    );
    
    SELECT 'success' as status, '设置成功' as message;
END$$

DELIMITER ;

-- 8. 创建视图：方便查询用户抽奖率配置
CREATE OR REPLACE VIEW `v_user_prize_configs` AS
SELECT 
    upc.id,
    upc.user_id,
    u.username,
    u.platform_id,
    u.custom_lottery_enabled,
    upc.prize_configs,
    upc.total_probability,
    upc.is_enabled,
    upc.created_at,
    upc.updated_at
FROM user_prize_configs upc
LEFT JOIN users u ON upc.user_id = u.id;

-- 9. 验证数据
SELECT 'Migration completed successfully!' as status;
SELECT COUNT(*) as total_users_with_configs FROM user_prize_configs;
SELECT COUNT(*) as total_users FROM users;

-- 显示示例数据
SELECT 
    user_id,
    JSON_PRETTY(prize_configs) as formatted_configs,
    total_probability,
    is_enabled
FROM user_prize_configs 
LIMIT 3;
