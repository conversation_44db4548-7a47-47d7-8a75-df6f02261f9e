// 导入所有模型
const User = require('./User')
const Admin = require('./Admin')
const LotteryPeriod = require('./LotteryPeriod')
const PrizeConfig = require('./PrizeConfig')
const LotteryRecord = require('./LotteryRecord')
const UserLotteryLimit = require('./UserLotteryLimit')
const SystemSetting = require('./SystemSetting')
const Banner = require('./Banner')
const AdminLog = require('./AdminLog')
const UserPrizeConfig = require('./UserPrizeConfig')

// 建立模型关联
LotteryPeriod.hasMany(PrizeConfig, { foreignKey: 'periodId', as: 'prizes' })
PrizeConfig.belongsTo(LotteryPeriod, { foreignKey: 'periodId', as: 'period' })

LotteryPeriod.hasMany(LotteryRecord, { foreignKey: 'periodId', as: 'records' })
LotteryRecord.belongsTo(LotteryPeriod, { foreignKey: 'periodId', as: 'period' })

User.hasMany(LotteryRecord, { foreignKey: 'userId', as: 'records' })
LotteryRecord.belongsTo(User, { foreignKey: 'userId', as: 'user' })

User.hasMany(UserLotteryLimit, { foreignKey: 'userId', as: 'limits' })
UserLotteryLimit.belongsTo(User, { foreignKey: 'userId', as: 'user' })

LotteryPeriod.hasMany(UserLotteryLimit, { foreignKey: 'periodId', as: 'userLimits' })
UserLotteryLimit.belongsTo(LotteryPeriod, { foreignKey: 'periodId', as: 'period' })

// 管理员日志关联
Admin.hasMany(AdminLog, { foreignKey: 'adminId', as: 'logs' })
AdminLog.belongsTo(Admin, { foreignKey: 'adminId', as: 'admin' })

// 用户抽奖率配置关联 (JSON格式，一对一关系)
User.hasOne(UserPrizeConfig, { foreignKey: 'userId', as: 'prizeConfig' })
UserPrizeConfig.belongsTo(User, { foreignKey: 'userId', as: 'user' })

// 导出所有模型
module.exports = {
  User,
  Admin,
  LotteryPeriod,
  PrizeConfig,
  LotteryRecord,
  UserLotteryLimit,
  SystemSetting,
  Banner,
  AdminLog,
  UserPrizeConfig
}
