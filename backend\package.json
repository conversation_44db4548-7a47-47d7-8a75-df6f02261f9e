{"name": "lottery-backend", "version": "1.0.0", "description": "抽奖平台后端API", "main": "server.js", "scripts": {"start": "node src/app.js", "dev": "nodemon src/app.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["lottery", "api", "express", "sequelize"], "author": "", "license": "ISC", "type": "commonjs", "dependencies": {"bcryptjs": "^3.0.2", "cors": "^2.8.5", "dotenv": "^17.2.1", "express": "^5.1.0", "jsonwebtoken": "^9.0.2", "mysql2": "^3.14.2", "sequelize": "^6.37.7"}, "devDependencies": {"nodemon": "^3.0.2"}}