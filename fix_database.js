const mysql = require('mysql2/promise');
require('dotenv').config({ path: './backend/.env' });

async function fixDatabase() {
  let connection;
  
  try {
    // 创建数据库连接
    connection = await mysql.createConnection({
      host: process.env.DB_HOST || 'localhost',
      user: process.env.DB_USER || 'root',
      password: process.env.DB_PASSWORD || '',
      database: process.env.DB_NAME || 'lottery_system'
    });

    console.log('✅ 数据库连接成功');

    // 1. 检查当前触发器
    console.log('\n📋 检查当前触发器...');
    const [triggers] = await connection.execute(`
      SELECT TRIGGER_NAME, EVENT_MANIPULATION, EVENT_OBJECT_TABLE 
      FROM information_schema.TRIGGERS 
      WHERE TRIGGER_SCHEMA = DATABASE()
    `);
    
    console.log('当前触发器:', triggers);

    // 2. 删除旧的触发器
    console.log('\n🗑️ 删除旧触发器...');
    try {
      await connection.execute('DROP TRIGGER IF EXISTS tr_users_after_insert');
      await connection.execute('DROP TRIGGER IF EXISTS tr_prize_configs_after_insert');
      await connection.execute('DROP TRIGGER IF EXISTS tr_users_after_insert_json');
      await connection.execute('DROP TRIGGER IF EXISTS tr_prize_configs_after_insert_json');
      console.log('✅ 旧触发器删除完成');
    } catch (error) {
      console.log('⚠️ 删除触发器时出现错误（可能不存在）:', error.message);
    }

    // 3. 检查user_prize_configs表结构
    console.log('\n📋 检查user_prize_configs表结构...');
    try {
      const [columns] = await connection.execute('DESCRIBE user_prize_configs');
      console.log('当前表结构:', columns.map(col => col.Field));
      
      // 检查是否是JSON格式
      const hasJsonColumn = columns.some(col => col.Field === 'prize_configs' && col.Type === 'json');
      
      if (!hasJsonColumn) {
        console.log('\n🔄 需要执行JSON格式迁移...');
        
        // 备份现有数据
        console.log('📦 备份现有数据...');
        await connection.execute('DROP TABLE IF EXISTS user_prize_configs_backup');
        await connection.execute('CREATE TABLE user_prize_configs_backup AS SELECT * FROM user_prize_configs WHERE 1=1');
        
        // 删除现有表
        console.log('🗑️ 删除现有表...');
        await connection.execute('DROP TABLE IF EXISTS user_prize_configs');
        
        // 创建新的JSON格式表
        console.log('🆕 创建新的JSON格式表...');
        await connection.execute(`
          CREATE TABLE user_prize_configs (
            id bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
            user_id bigint NOT NULL COMMENT '用户ID',
            prize_configs json NOT NULL COMMENT '奖品配置JSON数据',
            total_probability decimal(5,4) NOT NULL DEFAULT '1.0000' COMMENT '总概率(应该等于1.0000)',
            is_enabled tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否启用个人设置(0:使用全局设置, 1:使用个人设置)',
            created_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
            updated_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
            PRIMARY KEY (id),
            UNIQUE KEY uk_user_id (user_id) COMMENT '用户ID唯一索引',
            KEY idx_enabled (is_enabled) COMMENT '启用状态索引',
            KEY idx_total_probability (total_probability) COMMENT '总概率索引',
            CONSTRAINT fk_user_prize_configs_user_id FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE
          ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='用户个人抽奖率配置表(JSON格式)'
        `);
        
        // 为现有用户创建默认配置
        console.log('👥 为现有用户创建默认配置...');
        await connection.execute(`
          INSERT INTO user_prize_configs (user_id, prize_configs, total_probability, is_enabled)
          SELECT 
              u.id as user_id,
              JSON_OBJECT(
                  'configs', JSON_ARRAYAGG(
                      JSON_OBJECT(
                          'prizeConfigId', pc.id,
                          'prizeName', pc.prize_name,
                          'prizeAmount', pc.prize_amount,
                          'globalProbability', pc.probability,
                          'customProbability', pc.probability,
                          'isEnabled', true
                      )
                  )
              ) as prize_configs,
              (SELECT SUM(probability) FROM prize_configs) as total_probability,
              0 as is_enabled
          FROM users u
          CROSS JOIN (SELECT 1) dummy
          WHERE NOT EXISTS (
              SELECT 1 FROM user_prize_configs upc 
              WHERE upc.user_id = u.id
          )
          GROUP BY u.id
        `);
        
        console.log('✅ JSON格式迁移完成');
      } else {
        console.log('✅ 表已经是JSON格式');
      }
    } catch (error) {
      console.log('❌ 检查表结构失败:', error.message);
    }

    // 4. 创建新的JSON格式触发器
    console.log('\n🔧 创建新的JSON格式触发器...');
    
    // 用户注册触发器
    await connection.execute(`
      CREATE TRIGGER tr_users_after_insert_json
      AFTER INSERT ON users
      FOR EACH ROW
      BEGIN
          DECLARE total_prob DECIMAL(5,4) DEFAULT 1.0000;
          
          -- 计算当前奖品配置的总概率
          SELECT COALESCE(SUM(probability), 1.0000) INTO total_prob FROM prize_configs;
          
          -- 为新用户创建默认的JSON格式抽奖率配置
          INSERT INTO user_prize_configs (user_id, prize_configs, total_probability, is_enabled)
          SELECT 
              NEW.id as user_id,
              JSON_OBJECT(
                  'configs', JSON_ARRAYAGG(
                      JSON_OBJECT(
                          'prizeConfigId', pc.id,
                          'prizeName', pc.prize_name,
                          'prizeAmount', pc.prize_amount,
                          'globalProbability', pc.probability,
                          'customProbability', pc.probability,
                          'isEnabled', true
                      )
                  )
              ) as prize_configs,
              total_prob as total_probability,
              0 as is_enabled
          FROM prize_configs pc
          WHERE EXISTS (SELECT 1 FROM prize_configs LIMIT 1);
      END
    `);

    console.log('✅ 触发器创建完成');

    // 5. 验证修复结果
    console.log('\n🔍 验证修复结果...');
    const [userCount] = await connection.execute('SELECT COUNT(*) as count FROM users');
    const [configCount] = await connection.execute('SELECT COUNT(*) as count FROM user_prize_configs');
    
    console.log(`用户总数: ${userCount[0].count}`);
    console.log(`配置总数: ${configCount[0].count}`);
    
    if (userCount[0].count > configCount[0].count) {
      console.log('⚠️ 有用户缺少配置，正在补充...');
      await connection.execute(`
        INSERT INTO user_prize_configs (user_id, prize_configs, total_probability, is_enabled)
        SELECT 
            u.id as user_id,
            JSON_OBJECT(
                'configs', JSON_ARRAYAGG(
                    JSON_OBJECT(
                        'prizeConfigId', pc.id,
                        'prizeName', pc.prize_name,
                        'prizeAmount', pc.prize_amount,
                        'globalProbability', pc.probability,
                        'customProbability', pc.probability,
                        'isEnabled', true
                    )
                )
            ) as prize_configs,
            (SELECT SUM(probability) FROM prize_configs) as total_probability,
            0 as is_enabled
        FROM users u
        CROSS JOIN (SELECT 1) dummy
        WHERE NOT EXISTS (
            SELECT 1 FROM user_prize_configs upc 
            WHERE upc.user_id = u.id
        )
        GROUP BY u.id
      `);
      console.log('✅ 配置补充完成');
    }

    console.log('\n🎉 数据库修复完成！');

  } catch (error) {
    console.error('❌ 数据库修复失败:', error);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

fixDatabase();
