import axios from 'axios'

// API配置
export const API_BASE_URL = 'http://localhost:3000/api'

// 创建axios实例
const apiClient = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
})

// 请求拦截器 - 自动添加token
apiClient.interceptors.request.use(
  (config) => {
    // 优先使用管理员token，如果没有则使用普通用户token
    const adminToken = localStorage.getItem('adminToken')
    const userToken = localStorage.getItem('token')

    const token = adminToken || userToken
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// 响应拦截器 - 处理错误
apiClient.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      console.log('🔐 收到401错误，清理认证信息:', error.response?.data)

      // token过期或无效，清除本地存储
      const adminToken = localStorage.getItem('adminToken')
      if (adminToken) {
        // 清除管理员相关数据
        localStorage.removeItem('adminToken')
        localStorage.removeItem('admin')
        console.log('🗑️ 已清理管理员认证信息')
      } else {
        // 清除普通用户相关数据
        localStorage.removeItem('token')
        localStorage.removeItem('user')
        console.log('🗑️ 已清理用户认证信息')
      }

      // 刷新页面以重置认证状态
      if (error.response?.data?.code === 'TOKEN_EXPIRED') {
        console.log('⏰ Token已过期，将刷新页面')
        setTimeout(() => {
          window.location.reload()
        }, 1000)
      }
    }
    return Promise.reject(error)
  }
)

// API端点
export const API_ENDPOINTS = {
  // 用户认证相关
  register: '/auth/register',
  login: '/auth/login',
  verifyToken: '/auth/verify',

  // 抽奖相关
  currentPeriod: '/lottery/current-period',
  userLimit: '/lottery/user-limit',
  lotteryDraw: '/lottery/draw',
  lotteryRecords: '/lottery/records',
  userStatus: '/lottery/user-status',

  // 管理员认证相关
  adminLogin: '/admin/auth/login',
  adminLogout: '/admin/auth/logout',
  adminVerify: '/admin/auth/verify',
  adminChangePassword: '/admin/auth/change-password',

  // 管理后台相关
  adminDashboard: '/admin/dashboard/stats',
  adminActivities: '/admin/dashboard/activities',
  adminUsers: '/admin/users',
  adminUpdateUser: (id: number) => `/admin/users/${id}`,
  adminUserPrizeConfigs: (id: number) => `/admin/users/${id}/prize-configs`,
  adminUpdateUserPrizeConfigs: (id: number) => `/admin/users/${id}/prize-configs`,
  adminSetUserNoWin: (id: number) => `/admin/users/${id}/set-no-win`,
  adminLotteryRecords: '/admin/lottery-records',
  adminLotteryPeriods: '/admin/lottery-periods',
  adminCreatePeriod: '/admin/lottery-periods',
  adminUpdatePeriodStatus: (id: number) => `/admin/lottery-periods/${id}/status`,
  adminSettings: '/admin/settings',
  adminAdmins: '/admin/admins',
  adminCreateAdmin: '/admin/admins',
  adminUpdateAdmin: (id: number) => `/admin/admins/${id}`,
  adminDeleteAdmin: (id: number) => `/admin/admins/${id}`,

  // 数据管理
  adminCleanData: '/admin/data/clean',
  adminExportUsers: '/admin/data/export/users',
  adminExportLottery: '/admin/data/export/lottery',
  adminResetData: '/admin/data/reset',

  // 管理员日志
  adminLogs: '/admin/logs',
  adminLogDetail: (id: number) => `/admin/logs/${id}`
}

// 用户API
export const userAPI = {
  // 用户认证
  register: (userData: { platformId: string; username: string; password: string }) =>
    apiClient.post(API_ENDPOINTS.register, userData),

  login: (credentials: { username: string; password: string }) =>
    apiClient.post(API_ENDPOINTS.login, credentials),

  verifyToken: () =>
    apiClient.get(API_ENDPOINTS.verifyToken),

  // 抽奖功能
  getCurrentPeriod: () =>
    apiClient.get(API_ENDPOINTS.currentPeriod),

  getUserLimit: (periodId: number) =>
    apiClient.get(API_ENDPOINTS.userLimit, { params: { periodId } }),

  drawLottery: (periodId: number) =>
    apiClient.post(API_ENDPOINTS.lotteryDraw, { periodId }),

  getLotteryRecords: (params?: { page?: number; limit?: number; periodId?: number }) =>
    apiClient.get(API_ENDPOINTS.lotteryRecords, { params }),

  getUserStatus: () =>
    apiClient.get(API_ENDPOINTS.userStatus),
}

// 管理员API
export const adminAPI = {
  // 管理员认证
  login: (credentials: { username: string; password: string }) =>
    apiClient.post(API_ENDPOINTS.adminLogin, credentials),

  logout: () =>
    apiClient.post(API_ENDPOINTS.adminLogout),

  verify: () =>
    apiClient.get(API_ENDPOINTS.adminVerify),

  changePassword: (passwordData: { currentPassword: string; newPassword: string }) =>
    apiClient.put(API_ENDPOINTS.adminChangePassword, passwordData),

  // 仪表板
  getDashboardStats: () =>
    apiClient.get(API_ENDPOINTS.adminDashboard),

  getRecentActivities: (limit?: number) =>
    apiClient.get(API_ENDPOINTS.adminActivities, { params: { limit } }),

  // 用户管理
  getUsers: (params?: { page?: number; limit?: number; search?: string; status?: string }) =>
    apiClient.get(API_ENDPOINTS.adminUsers, { params }),

  updateUser: (id: number, userData: { status?: string; totalWinnings?: number }) =>
    apiClient.put(API_ENDPOINTS.adminUpdateUser(id), userData),

  // 用户抽奖率配置管理
  getUserPrizeConfigs: (id: number) =>
    apiClient.get(API_ENDPOINTS.adminUserPrizeConfigs(id)),

  updateUserPrizeConfigs: (id: number, configData: { configs: any[]; customLotteryEnabled: boolean; totalProbability: number }) =>
    apiClient.put(API_ENDPOINTS.adminUpdateUserPrizeConfigs(id), configData),

  setUserNoWin: (id: number) =>
    apiClient.post(API_ENDPOINTS.adminSetUserNoWin(id)),

  // 抽奖记录管理
  getLotteryRecords: (params?: { page?: number; limit?: number; periodId?: number; isWinner?: boolean; startDate?: string; endDate?: string }) =>
    apiClient.get(API_ENDPOINTS.adminLotteryRecords, { params }),

  distributePrize: (recordId: number, remark: string = '') =>
    apiClient.put(`/admin/lottery-records/${recordId}/distribute`, { remark }),

  // 期次管理
  getLotteryPeriods: (params?: { page?: number; limit?: number; status?: string }) =>
    apiClient.get(API_ENDPOINTS.adminLotteryPeriods, { params }),

  createLotteryPeriod: (periodData: { periodName: string; startTime: string; endTime: string; drawTime: string; maxAttemptsPerUser?: number; totalPrizeAmount?: number; actualDrawableAmount?: number; totalShares?: number; prizes?: any[] }) =>
    apiClient.post(API_ENDPOINTS.adminCreatePeriod, periodData),

  updatePeriodStatus: (id: number, status: string) =>
    apiClient.put(API_ENDPOINTS.adminUpdatePeriodStatus(id), { status }),

  // 系统设置
  getSettings: () =>
    apiClient.get(API_ENDPOINTS.adminSettings),

  updateSettings: (settingsData: { systemSettings?: any; prizeConfig?: any[] }) =>
    apiClient.put(API_ENDPOINTS.adminSettings, settingsData),

  // 管理员管理
  getAdmins: (params?: { page?: number; limit?: number }) =>
    apiClient.get(API_ENDPOINTS.adminAdmins, { params }),

  createAdmin: (adminData: { username: string; password: string; email?: string; role?: string }) =>
    apiClient.post(API_ENDPOINTS.adminCreateAdmin, adminData),

  updateAdmin: (id: number, adminData: { username?: string; email?: string; role?: string; status?: string; password?: string }) =>
    apiClient.put(API_ENDPOINTS.adminUpdateAdmin(id), adminData),

  deleteAdmin: (id: number) =>
    apiClient.delete(API_ENDPOINTS.adminDeleteAdmin(id)),

  // 数据管理
  cleanExpiredData: () =>
    apiClient.delete(API_ENDPOINTS.adminCleanData),

  exportUserData: () =>
    apiClient.get(API_ENDPOINTS.adminExportUsers),

  exportLotteryData: () =>
    apiClient.get(API_ENDPOINTS.adminExportLottery),

  resetAllData: () =>
    apiClient.delete(API_ENDPOINTS.adminResetData),

  // 管理员日志
  getLogs: (params?: { page?: number; limit?: number; search?: string; action?: string; date?: string; riskLevel?: string }) =>
    apiClient.get(API_ENDPOINTS.adminLogs, { params }),

  getLogDetail: (id: number) =>
    apiClient.get(API_ENDPOINTS.adminLogDetail(id))
}
