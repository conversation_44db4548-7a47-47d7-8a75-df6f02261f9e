const express = require('express')
const bcrypt = require('bcryptjs')
const jwt = require('jsonwebtoken')
const { Op } = require('sequelize')
const { sequelize } = require('../config/database')
const { User, PrizeConfig, UserPrizeConfig } = require('../models')
const { JWT_SECRET } = require('../middleware/auth')

const router = express.Router()

/**
 * 用户注册
 * POST /api/auth/register
 */
router.post('/register', async (req, res) => {
  try {
    const { platformId, username, password } = req.body

    // 参数验证
    if (!platformId || !username || !password) {
      return res.status(400).json({ error: '请填写完整的注册信息' })
    }

    if (password.length < 6) {
      return res.status(400).json({ error: '密码长度至少6位' })
    }

    // 检查用户是否已存在
    const existingUser = await User.findOne({
      where: {
        [Op.or]: [
          { platformId },
          { username }
        ]
      }
    })

    if (existingUser) {
      if (existingUser.platformId === platformId) {
        return res.status(400).json({ error: '平台ID已存在' })
      }
      if (existingUser.username === username) {
        return res.status(400).json({ error: '用户名已存在' })
      }
    }

    // 加密密码
    const hashedPassword = await bcrypt.hash(password, 10)

    // 开始事务
    const transaction = await sequelize.transaction()

    try {
      // 创建用户
      const user = await User.create({
        platformId,
        username,
        passwordHash: hashedPassword,
        status: '正常'
      }, { transaction })

      // 获取当前奖品配置
      const prizeConfigs = await PrizeConfig.findAll({ transaction })

      if (prizeConfigs.length > 0) {
        // 为新用户创建JSON格式的抽奖率配置
        const userPrizeConfigData = {
          configs: prizeConfigs.map(pc => ({
            prizeConfigId: pc.id,
            prizeName: pc.prizeName,
            prizeAmount: parseFloat(pc.prizeAmount),
            globalProbability: parseFloat(pc.probability),
            customProbability: parseFloat(pc.probability),
            isEnabled: true
          }))
        }

        const totalProbability = prizeConfigs.reduce((sum, pc) => sum + parseFloat(pc.probability), 0)

        await UserPrizeConfig.create({
          userId: user.id,
          prizeConfigs: userPrizeConfigData,
          totalProbability: totalProbability,
          isEnabled: false
        }, { transaction })
      }

      await transaction.commit()

      res.status(201).json({
        success: true,
        message: '注册成功',
        data: {
          id: user.id,
          platformId: user.platformId,
          username: user.username,
          status: user.status
        }
      })
    } catch (transactionError) {
      await transaction.rollback()
      throw transactionError
    }
  } catch (error) {
    console.error('用户注册错误:', error)
    res.status(500).json({ error: '注册失败，请稍后重试' })
  }
})

/**
 * 用户登录
 * POST /api/auth/login
 */
router.post('/login', async (req, res) => {
  try {
    const { username, password } = req.body

    // 参数验证
    if (!username || !password) {
      return res.status(400).json({ error: '请填写用户名和密码' })
    }

    // 查找用户
    const user = await User.findOne({ where: { username } })
    if (!user) {
      return res.status(401).json({ error: '用户名或密码错误' })
    }

    // 检查用户状态
    if (user.status === '禁用') {
      return res.status(401).json({ error: '账户已被禁用，请联系管理员' })
    }

    // 验证密码
    const isValidPassword = await bcrypt.compare(password, user.passwordHash)
    if (!isValidPassword) {
      return res.status(401).json({ error: '用户名或密码错误' })
    }

    // 生成JWT令牌
    const token = jwt.sign(
      {
        userId: user.id,
        platformId: user.platformId,
        username: user.username,
        isAdmin: false
      },
      JWT_SECRET,
      { expiresIn: '24h' }
    )

    res.json({
      success: true,
      message: '登录成功',
      data: {
        token,
        user: {
          id: user.id,
          platformId: user.platformId,
          username: user.username,
          status: user.status,
          totalWinnings: parseFloat(user.totalWinnings)
        }
      }
    })
  } catch (error) {
    console.error('用户登录错误:', error)
    res.status(500).json({ error: '登录失败，请稍后重试' })
  }
})

/**
 * 验证token有效性
 * GET /api/auth/verify
 */
router.get('/verify', async (req, res) => {
  try {
    const authHeader = req.headers['authorization']
    const token = authHeader && authHeader.split(' ')[1]

    if (!token) {
      return res.status(401).json({ error: '未提供token' })
    }

    const decoded = jwt.verify(token, JWT_SECRET)
    
    // 查找用户确认状态
    const user = await User.findByPk(decoded.userId)
    if (!user || user.status === '禁用') {
      return res.status(401).json({ error: '用户状态异常' })
    }

    res.json({
      success: true,
      data: {
        user: {
          id: user.id,
          platformId: user.platformId,
          username: user.username,
          status: user.status,
          totalWinnings: parseFloat(user.totalWinnings)
        }
      }
    })
  } catch (error) {
    console.error('Token验证错误:', error)
    res.status(401).json({ error: '无效的token' })
  }
})

module.exports = router
