const mysql = require('mysql2/promise');
require('dotenv').config();

async function verifyUserConfig() {
  let connection;
  
  try {
    connection = await mysql.createConnection({
      host: process.env.DB_HOST || 'localhost',
      user: process.env.DB_USER || 'root',
      password: process.env.DB_PASSWORD || '',
      database: process.env.DB_NAME || 'lottery_system'
    });

    console.log('✅ 数据库连接成功');

    // 查询最新注册的用户
    const [users] = await connection.execute(`
      SELECT id, platform_id, username, custom_lottery_enabled 
      FROM users 
      ORDER BY created_at DESC 
      LIMIT 3
    `);
    
    console.log('\n📋 最新用户列表:');
    users.forEach(user => {
      console.log(`- ID: ${user.id}, 平台ID: ${user.platform_id}, 用户名: ${user.username}, 个人抽奖: ${user.custom_lottery_enabled}`);
    });

    // 查询用户的抽奖率配置
    const [configs] = await connection.execute(`
      SELECT 
        upc.user_id,
        upc.prize_configs,
        upc.total_probability,
        upc.is_enabled,
        u.username
      FROM user_prize_configs upc
      JOIN users u ON upc.user_id = u.id
      ORDER BY upc.user_id DESC
      LIMIT 3
    `);

    console.log('\n🎯 用户抽奖率配置:');
    configs.forEach(config => {
      console.log(`\n用户: ${config.username} (ID: ${config.user_id})`);
      console.log(`总概率: ${config.total_probability}`);
      console.log(`启用状态: ${config.is_enabled ? '已启用' : '未启用'}`);
      console.log(`配置详情:`, JSON.stringify(config.prize_configs, null, 2));
    });

  } catch (error) {
    console.error('❌ 验证失败:', error);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

console.log('🔍 开始验证用户配置...');
verifyUserConfig();
