# 抽奖平台后端 - 重构版本

## 📋 项目概述

这是抽奖平台的后端重构版本，采用模块化架构设计，将原本单一文件的API结构拆分为多个模块，提高代码的可维护性和可扩展性。

## 🏗️ 项目结构

```
backend/
├── src/
│   ├── app.js                 # 主应用入口文件
│   ├── config/
│   │   └── database.js        # 数据库配置
│   ├── models/                # 数据模型
│   │   ├── index.js           # 模型关联配置
│   │   ├── User.js            # 用户模型
│   │   ├── Admin.js           # 管理员模型
│   │   ├── LotteryPeriod.js   # 抽奖期次模型
│   │   ├── PrizeConfig.js     # 奖品配置模型
│   │   ├── LotteryRecord.js   # 抽奖记录模型
│   │   └── UserLotteryLimit.js # 用户抽奖限制模型
│   ├── routes/                # 路由模块
│   │   ├── auth.js            # 前台用户认证路由
│   │   └── admin/
│   │       └── auth.js        # 后台管理员认证路由
│   ├── middleware/
│   │   └── auth.js            # 认证中间件
│   └── utils/
│       └── database.js        # 数据库工具函数
├── database/                  # 数据库脚本
│   ├── create_tables.sql      # 创建表结构
│   └── init_data.sql          # 初始化数据

├── package.json
└── README.md
```

## 🚀 启动方式

```bash
npm run dev    # 开发模式
npm start      # 生产模式
```

## 📊 数据库配置

### 1. 使用Navicat创建数据库

1. 打开Navicat for MySQL
2. 连接到你的MySQL服务器
3. 执行 `database/create_tables.sql` 创建表结构
4. 执行 `database/init_data.sql` 初始化基础数据

### 2. 环境变量配置

在 `backend` 目录下创建 `.env` 文件：

```env
# 数据库配置
DB_HOST=localhost
DB_PORT=3306
DB_NAME=lottery_system
DB_USER=root
DB_PASSWORD=your_password

# JWT密钥
JWT_SECRET=your-secret-key

# 服务器配置
PORT=3000
NODE_ENV=development
FRONTEND_URL=http://localhost:5173
```

## 🔐 API接口

### 前台用户接口

**基础路径**: `/api/auth`

- `POST /register` - 用户注册
- `POST /login` - 用户登录
- `GET /verify` - 验证token

### 后台管理员接口

**基础路径**: `/api/admin/auth`

- `POST /login` - 管理员登录
- `POST /logout` - 管理员登出
- `GET /verify` - 验证管理员token
- `PUT /change-password` - 修改管理员密码

### 系统接口

- `GET /health` - 健康检查

## 👥 默认账户

### 管理员账户
- **用户名**: `admin`
- **密码**: `admin123`
- **角色**: 超级管理员

### 测试用户账户
- **用户名**: `testuser`
- **密码**: `123456`
- **平台ID**: `TEST001`

## 🔧 技术栈

- **框架**: Express.js
- **数据库**: MySQL + Sequelize ORM
- **认证**: JWT + bcryptjs
- **开发工具**: nodemon
- **其他**: cors, dotenv

## 📈 重构改进

### 1. 模块化架构
- 将单一文件拆分为多个模块
- 按功能划分路由和中间件
- 提高代码可维护性

### 2. 数据模型优化
- 使用Sequelize ORM管理数据模型
- 建立模型关联关系
- 统一字段映射和验证

### 3. 认证系统增强
- 分离用户和管理员认证逻辑
- 增强JWT token验证
- 添加角色权限控制

### 4. 错误处理改进
- 统一错误处理中间件
- 详细的错误日志记录
- 友好的错误响应格式

### 5. 数据库管理
- 提供SQL脚本便于数据库初始化
- 自动化数据库连接测试
- 数据库初始化工具函数

## 🔄 迁移指南

### 从旧版本迁移

1. **备份数据**: 确保现有数据已备份
2. **更新依赖**: 运行 `npm install` 安装新依赖
3. **配置环境**: 创建 `.env` 文件配置数据库连接
4. **初始化数据库**: 使用提供的SQL脚本创建表结构
5. **启动新版本**: 使用 `npm run dev` 启动新版本
6. **测试接口**: 验证所有API接口正常工作

### 前端适配

前端需要更新API调用地址：
- 用户登录: `/api/auth/login`
- 管理员登录: `/api/admin/auth/login`
- Token验证: `/api/auth/verify` 或 `/api/admin/auth/verify`

## 🐛 故障排除

### 常见问题

1. **数据库连接失败**
   - 检查 `.env` 文件配置
   - 确认MySQL服务正在运行
   - 验证数据库用户权限

2. **JWT token错误**
   - 检查 `JWT_SECRET` 环境变量
   - 确认token格式正确
   - 验证token未过期

3. **端口冲突**
   - 修改 `.env` 中的 `PORT` 配置
   - 确认端口未被其他程序占用

## 📝 开发日志

- **2024-07-27**: 完成后端重构
  - 拆分单文件架构为模块化结构
  - 分离用户和管理员认证接口
  - 创建数据库初始化脚本
  - 添加完整的错误处理和日志记录

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

## 📄 许可证

MIT License
