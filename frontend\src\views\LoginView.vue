<script setup lang="ts">
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { useAuthStore } from '../stores/auth'

const router = useRouter()
const authStore = useAuthStore()

// 表单数据
const loginForm = ref({
  username: '',
  password: ''
})

// 表单验证规则
const rules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 3, max: 20, message: '用户名长度在 3 到 20 个字符', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, max: 20, message: '密码长度在 6 到 20 个字符', trigger: 'blur' }
  ]
}

const loading = ref(false)

// 登录处理
const handleLogin = async () => {
  if (!loginForm.value.username || !loginForm.value.password) {
    ElMessage.error('请填写完整信息')
    return
  }

  loading.value = true

  try {
    const result = await authStore.login(loginForm.value.username, loginForm.value.password)

    if (result.success) {
      ElMessage.success(`登录成功！欢迎 ${loginForm.value.username}`)

      // 立即跳转到首页
      router.push('/')
    } else {
      ElMessage.error(result.message || '登录失败，请重试')
    }

  } catch (error: any) {
    ElMessage.error(error.message || '登录失败，请重试')
  } finally {
    loading.value = false
  }
}

// 跳转到注册页面
const goToRegister = () => {
  router.push('/register')
}

// 返回首页
const goHome = () => {
  router.push('/')
}
</script>

<template>
  <div class="min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-black flex items-center justify-center p-4">
    <div class="w-full max-w-md">
      <!-- 返回按钮 -->
      <div class="mb-6">
        <button @click="goHome" 
                class="flex items-center text-gold-400 hover:text-gold-300 transition-colors">
          <span class="mr-2">←</span>
          返回首页
        </button>
      </div>

      <!-- 登录卡片 -->
      <div class="bg-gray-800 rounded-2xl p-8 shadow-2xl border border-gray-700">
        <!-- 标题 -->
        <div class="text-center mb-8">
          <div class="text-4xl mb-4">🎰</div>
          <h1 class="text-2xl md:text-3xl font-bold text-gold-400 mb-2">抽奖活动登录</h1>
          <p class="text-gray-400">登录后即可参与抽奖活动</p>
        </div>

        <!-- 登录表单 -->
        <form @submit.prevent="handleLogin" class="space-y-6">
          <!-- 用户名 -->
          <div>
            <label class="block text-sm font-medium text-gray-300 mb-2">用户名</label>
            <input
              v-model="loginForm.username"
              type="text"
              placeholder="请输入您的用户名"
              class="w-full px-4 py-3 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-gold-500 focus:border-transparent transition-all"
              :disabled="loading"
            />
          </div>

          <!-- 密码输入 -->
          <div>
            <label class="block text-sm font-medium text-gray-300 mb-2">密码</label>
            <input 
              v-model="loginForm.password"
              type="password" 
              placeholder="请输入您的密码"
              class="w-full px-4 py-3 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-gold-500 focus:border-transparent transition-all"
              :disabled="loading"
            />
          </div>

          <!-- 登录按钮 -->
          <button 
            type="submit"
            :disabled="loading"
            class="w-full py-3 bg-gradient-to-r from-gold-500 to-gold-600 hover:from-gold-600 hover:to-gold-700 disabled:from-gray-600 disabled:to-gray-700 text-black font-bold rounded-lg transition-all duration-300 transform hover:scale-105 disabled:transform-none disabled:cursor-not-allowed"
          >
            <span v-if="loading" class="flex items-center justify-center">
              <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-black" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              登录中...
            </span>
            <span v-else>🎯 立即登录</span>
          </button>
        </form>

        <!-- 分割线 -->
        <div class="my-6 flex items-center">
          <div class="flex-1 border-t border-gray-600"></div>
          <span class="px-4 text-gray-400 text-sm">或</span>
          <div class="flex-1 border-t border-gray-600"></div>
        </div>

        <!-- 注册链接 -->
        <div class="text-center">
          <p class="text-gray-400 mb-4">还没有账号？</p>
          <button @click="goToRegister" 
                  class="w-full py-3 bg-gray-700 hover:bg-gray-600 text-white font-semibold rounded-lg transition-colors border border-gray-600">
            立即注册
          </button>
        </div>

        <!-- 提示信息 -->
        <div class="mt-6 p-4 bg-blue-900/30 border border-blue-700 rounded-lg">
          <div class="flex items-start">
            <span class="text-blue-400 mr-2">💡</span>
            <div class="text-sm text-blue-300">
              <p class="font-semibold mb-1">温馨提示：</p>
              <p>登录成功后将显示您在平台的账号ID，请确认信息正确后参与抽奖活动。</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
