<template>
  <div class="admin-settings min-h-screen bg-gradient-to-br from-gray-900 via-black to-gray-800 p-6">
    <!-- 页面头部 -->
    <div class="mb-8">
      <div class="flex items-center justify-between">
        <div>
          <h2 class="text-3xl font-bold text-white mb-2 flex items-center">
            <el-icon class="mr-3 text-gold-400" size="32"><Setting /></el-icon>
            系统设置
          </h2>
          <p class="text-gray-400">管理系统配置、奖品设置和平台参数</p>
        </div>
        <div class="flex items-center space-x-4">
          <button
            @click="handleRefresh"
            class="px-4 py-2 bg-blue-500/20 hover:bg-blue-500/30 border border-blue-500/30 text-blue-400 hover:text-blue-300 rounded-lg transition-all duration-200 flex items-center space-x-2"
          >
            <el-icon><Refresh /></el-icon>
            <span>刷新配置</span>
          </button>
        </div>
      </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <!-- 奖品配置 -->
      <div class="bg-gradient-to-br from-gray-800 to-gray-900 border border-gold-500/30 rounded-xl shadow-xl p-6">
        <div class="flex items-center justify-between mb-6">
          <h3 class="text-xl font-semibold text-white flex items-center">
            <el-icon class="mr-2 text-gold-400"><Trophy /></el-icon>
            奖品配置
          </h3>
          <div class="text-sm">
            <span class="text-gray-400">总概率: </span>
            <span :class="totalProbability === 100 ? 'text-green-400' : 'text-yellow-400'" class="font-medium">
              {{ totalProbability.toFixed(2) }}%
            </span>
            <span :class="totalProbability === 100 ? 'bg-green-500/20 border-green-500/30 text-green-400' : 'bg-yellow-500/20 border-yellow-500/30 text-yellow-400'" class="ml-2 px-2 py-1 border rounded text-xs">
              {{ totalProbability === 100 ? '正常' : '需调整' }}
            </span>
          </div>
        </div>

        <div class="space-y-3">
          <div v-if="prizeConfig.length === 0" class="text-center py-8">
            <div class="text-gray-400 mb-2">
              <el-icon size="48"><Trophy /></el-icon>
            </div>
            <p class="text-gray-400">暂无奖品配置数据</p>
            <p class="text-gray-500 text-sm mt-1">请联系管理员配置奖品信息</p>
          </div>

          <div v-for="(prize, index) in prizeConfig" :key="index" class="bg-gray-700/50 border border-gray-600 rounded-lg p-4 hover:border-gold-500/30 transition-all">
            <div class="flex justify-between items-center mb-3">
              <h4 class="font-medium text-white">{{ prize.name }}</h4>
              <span :class="getPrizeColor(prize.level)" class="px-2 py-1 border rounded text-xs font-medium">
                等级 {{ prize.level }}
              </span>
            </div>

            <div class="grid grid-cols-2 gap-3">
              <div>
                <label class="block text-sm text-gray-400 mb-1">奖品金额</label>
                <input
                  v-model="prize.amount"
                  type="number"
                  min="0"
                  step="0.01"
                  class="w-full px-3 py-2 bg-gray-600 border border-gray-500 text-white rounded focus:outline-none focus:border-gold-500 focus:ring-1 focus:ring-gold-500 transition-all"
                />
              </div>
              <div>
                <label class="block text-sm text-gray-400 mb-1">中奖概率</label>
                <div class="relative">
                  <input
                    v-model.number="prize.probability"
                    type="number"
                    min="0"
                    max="100"
                    step="0.0001"
                    placeholder="例如: 0.1 表示 0.1%"
                    @input="updateProbability(index, $event)"
                    class="w-full px-3 py-2 pr-8 bg-gray-600 border border-gray-500 text-white rounded focus:outline-none focus:border-gold-500 focus:ring-1 focus:ring-gold-500 transition-all"
                  />
                  <span class="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 text-sm">%</span>
                </div>
                <p class="text-xs text-gray-500 mt-1">输入百分比数值，如 0.1 表示 0.1% 中奖率</p>
              </div>
            </div>
          </div>
        </div>

        <div class="mt-6 flex justify-end">
          <button
            @click="savePrizeConfig"
            :disabled="prizeConfig.length === 0"
            :class="[
              'px-6 py-2 font-medium rounded-lg transition-all',
              prizeConfig.length === 0
                ? 'bg-gray-600 text-gray-400 cursor-not-allowed'
                : 'bg-gradient-to-r from-gold-500 to-gold-600 hover:from-gold-600 hover:to-gold-700 text-black'
            ]"
          >
            保存奖品配置
          </button>
        </div>
      </div>

      <!-- 系统参数 -->
      <div class="bg-gradient-to-br from-gray-800 to-gray-900 border border-gold-500/30 rounded-xl shadow-xl p-6">
        <h3 class="text-xl font-semibold text-white mb-6 flex items-center">
          <el-icon class="mr-2 text-gold-400"><Tools /></el-icon>
          系统参数
        </h3>

        <div class="space-y-4">
          <div>
            <label class="block text-sm font-medium text-gray-400 mb-2">平台名称</label>
            <input
              v-model="systemSettings.platformName"
              type="text"
              class="w-full px-4 py-3 bg-gray-700 border border-gray-600 text-white rounded-lg focus:outline-none focus:border-gold-500 focus:ring-1 focus:ring-gold-500 transition-all"
              placeholder="请输入平台名称"
            />
          </div>

          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label class="block text-sm font-medium text-gray-400 mb-2">客服QQ</label>
              <input
                v-model="systemSettings.customerServiceQQ"
                type="text"
                class="w-full px-4 py-3 bg-gray-700 border border-gray-600 text-white rounded-lg focus:outline-none focus:border-gold-500 focus:ring-1 focus:ring-gold-500 transition-all"
                placeholder="请输入客服QQ号"
              />
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-400 mb-2">客服微信</label>
              <input
                v-model="systemSettings.customerServiceWechat"
                type="text"
                class="w-full px-4 py-3 bg-gray-700 border border-gray-600 text-white rounded-lg focus:outline-none focus:border-gold-500 focus:ring-1 focus:ring-gold-500 transition-all"
                placeholder="请输入客服微信号"
              />
            </div>
          </div>

          <div>
            <label class="block text-sm font-medium text-gray-400 mb-2">默认抽奖次数</label>
            <input
              v-model="systemSettings.defaultMaxAttempts"
              type="number"
              min="1"
              max="10"
              class="w-full px-4 py-3 bg-gray-700 border border-gray-600 text-white rounded-lg focus:outline-none focus:border-gold-500 focus:ring-1 focus:ring-gold-500 transition-all"
            />
          </div>

          <div class="flex items-center justify-between p-4 bg-gray-700/50 border border-gray-600 rounded-lg">
            <div>
              <label class="block text-sm font-medium text-white mb-1">维护模式</label>
              <p class="text-xs text-gray-400">开启后用户将无法访问前台页面</p>
            </div>
            <label class="relative inline-flex items-center cursor-pointer">
              <input
                v-model="systemSettings.maintenanceMode"
                type="checkbox"
                class="sr-only peer"
              />
              <div class="w-11 h-6 bg-gray-600 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-gold-300/20 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-gold-500"></div>
            </label>
          </div>

          <div>
            <label class="block text-sm font-medium text-gray-400 mb-2">维护公告</label>
            <textarea
              v-model="systemSettings.maintenanceNotice"
              rows="3"
              class="w-full px-4 py-3 bg-gray-700 border border-gray-600 text-white rounded-lg focus:outline-none focus:border-gold-500 focus:ring-1 focus:ring-gold-500 transition-all resize-none"
              placeholder="维护期间显示的公告内容"
            ></textarea>
          </div>
        </div>

        <div class="mt-6 flex justify-end">
          <button
            @click="saveSystemSettings"
            :disabled="!systemSettings || Object.keys(systemSettings).length === 0"
            :class="[
              'px-6 py-2 font-medium rounded-lg transition-all',
              (!systemSettings || Object.keys(systemSettings).length === 0)
                ? 'bg-gray-600 text-gray-400 cursor-not-allowed'
                : 'bg-gradient-to-r from-gold-500 to-gold-600 hover:from-gold-600 hover:to-gold-700 text-black'
            ]"
          >
            保存系统设置
          </button>
        </div>
      </div>

      <!-- 广告横幅管理 -->
      <div class="bg-gradient-to-br from-gray-800 to-gray-900 border border-gold-500/30 rounded-xl shadow-xl p-6">
        <h3 class="text-xl font-semibold text-white mb-6 flex items-center">
          <el-icon class="mr-2 text-gold-400"><Picture /></el-icon>
          广告横幅管理
        </h3>

        <div class="space-y-4">
          <div v-if="banners.length === 0" class="text-center py-8">
            <div class="text-gray-400 mb-2">
              <el-icon size="48"><Picture /></el-icon>
            </div>
            <p class="text-gray-400">暂无横幅配置</p>
            <p class="text-gray-500 text-sm mt-1">点击下方按钮添加横幅</p>
          </div>

          <div v-for="(banner, index) in banners" :key="index" class="bg-gray-700/50 border border-gray-600 rounded-lg p-4 hover:border-gold-500/30 transition-all">
            <div class="flex justify-between items-start mb-4">
              <h4 class="font-medium text-white">横幅 {{ index + 1 }}</h4>
              <button
                @click="removeBanner(index)"
                class="px-3 py-1 bg-red-500/20 hover:bg-red-500/30 border border-red-500/30 text-red-400 hover:text-red-300 rounded transition-all text-sm"
              >
                删除
              </button>
            </div>

            <div class="space-y-3">
              <div>
                <label class="block text-sm text-gray-400 mb-1">标题</label>
                <input
                  v-model="banner.title"
                  type="text"
                  @input="debouncedSaveBanners"
                  class="w-full px-3 py-2 bg-gray-600 border border-gray-500 text-white rounded focus:outline-none focus:border-gold-500 focus:ring-1 focus:ring-gold-500 transition-all"
                  placeholder="请输入横幅标题"
                />
              </div>
              <div>
                <label class="block text-sm text-gray-400 mb-1">链接地址</label>
                <input
                  v-model="banner.url"
                  type="url"
                  @input="debouncedSaveBanners"
                  class="w-full px-3 py-2 bg-gray-600 border border-gray-500 text-white rounded focus:outline-none focus:border-gold-500 focus:ring-1 focus:ring-gold-500 transition-all"
                  placeholder="请输入链接地址"
                />
              </div>
              <div>
                <label class="block text-sm text-gray-400 mb-1">图片地址</label>
                <input
                  v-model="banner.imageUrl"
                  type="url"
                  @input="debouncedSaveBanners"
                  class="w-full px-3 py-2 bg-gray-600 border border-gray-500 text-white rounded focus:outline-none focus:border-gold-500 focus:ring-1 focus:ring-gold-500 transition-all"
                  placeholder="请输入图片地址"
                />
              </div>
              <div class="flex items-center justify-between">
                <div class="flex items-center space-x-2">
                  <label class="relative inline-flex items-center cursor-pointer">
                    <input
                      v-model="banner.enabled"
                      type="checkbox"
                      @change="debouncedSaveBanners"
                      class="sr-only peer"
                    />
                    <div class="w-9 h-5 bg-gray-600 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-gold-300/20 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-4 after:w-4 after:transition-all peer-checked:bg-gold-500"></div>
                  </label>
                  <span class="text-sm text-gray-400">启用</span>
                </div>
                <div class="flex items-center space-x-2">
                  <span class="text-sm text-gray-400">排序:</span>
                  <input
                    v-model="banner.sort"
                    type="number"
                    min="0"
                    class="w-16 px-2 py-1 bg-gray-600 border border-gray-500 text-white rounded focus:outline-none focus:border-gold-500 text-center"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="mt-6 flex justify-between">
          <button
            @click="addBanner"
            class="px-4 py-2 bg-blue-500/20 hover:bg-blue-500/30 border border-blue-500/30 text-blue-400 hover:text-blue-300 rounded-lg transition-all flex items-center space-x-2"
          >
            <el-icon><Plus /></el-icon>
            <span>添加横幅</span>
          </button>
          <button
            @click="saveBanners"
            class="px-6 py-2 bg-gradient-to-r from-gold-500 to-gold-600 hover:from-gold-600 hover:to-gold-700 text-black font-medium rounded-lg transition-all"
          >
            保存横幅设置
          </button>
        </div>
      </div>

      <!-- 数据管理 -->
      <div class="bg-gradient-to-br from-gray-800 to-gray-900 border border-gold-500/30 rounded-xl shadow-xl p-6">
        <h3 class="text-xl font-semibold text-white mb-6 flex items-center">
          <el-icon class="mr-2 text-gold-400"><DataAnalysis /></el-icon>
          数据管理
        </h3>

        <div class="space-y-4">
          <div class="flex justify-between items-center p-4 bg-yellow-500/10 border border-yellow-500/30 rounded-lg hover:bg-yellow-500/20 transition-all">
            <div>
              <span class="text-yellow-400 font-medium">清理过期数据</span>
              <p class="text-gray-400 text-sm mt-1">清理30天前的过期日志和临时数据</p>
            </div>
            <button
              @click="cleanExpiredData"
              class="px-4 py-2 bg-yellow-500/20 hover:bg-yellow-500/30 border border-yellow-500/30 text-yellow-400 hover:text-yellow-300 rounded-lg transition-all"
            >
              执行清理
            </button>
          </div>

          <div class="flex justify-between items-center p-4 bg-blue-500/10 border border-blue-500/30 rounded-lg hover:bg-blue-500/20 transition-all">
            <div>
              <span class="text-blue-400 font-medium">导出用户数据</span>
              <p class="text-gray-400 text-sm mt-1">导出所有用户信息和统计数据</p>
            </div>
            <button
              @click="exportUserData"
              class="px-4 py-2 bg-blue-500/20 hover:bg-blue-500/30 border border-blue-500/30 text-blue-400 hover:text-blue-300 rounded-lg transition-all"
            >
              导出Excel
            </button>
          </div>

          <div class="flex justify-between items-center p-4 bg-green-500/10 border border-green-500/30 rounded-lg hover:bg-green-500/20 transition-all">
            <div>
              <span class="text-green-400 font-medium">导出抽奖记录</span>
              <p class="text-gray-400 text-sm mt-1">导出所有抽奖记录和中奖信息</p>
            </div>
            <button
              @click="exportLotteryData"
              class="px-4 py-2 bg-green-500/20 hover:bg-green-500/30 border border-green-500/30 text-green-400 hover:text-green-300 rounded-lg transition-all"
            >
              导出Excel
            </button>
          </div>

          <div class="flex justify-between items-center p-4 bg-red-500/10 border border-red-500/30 rounded-lg hover:bg-red-500/20 transition-all">
            <div>
              <span class="text-red-400 font-medium">重置所有数据</span>
              <p class="text-gray-400 text-sm mt-1">⚠️ 危险操作：将删除所有用户和抽奖记录</p>
            </div>
            <button
              @click="resetAllData"
              class="px-4 py-2 bg-red-500/20 hover:bg-red-500/30 border border-red-500/30 text-red-400 hover:text-red-300 rounded-lg transition-all"
            >
              重置数据
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Setting, Refresh, Trophy, Tools, Picture, Plus, DataAnalysis } from '@element-plus/icons-vue'
import { useAdminStore } from '@/stores/admin'
import { adminAPI } from '@/api/config'
import * as XLSX from 'xlsx'

const adminStore = useAdminStore()

// 奖品配置 - 直接使用 adminStore 的数据，并转换概率显示
const prizeConfig = computed({
  get: () => {
    const configs = adminStore.prizeConfig || []
    // 将数据库中的小数概率转换为百分比显示
    return configs.map(prize => ({
      ...prize,
      probability: parseFloat((prize.probability * 100).toFixed(4)) // 转换为百分比，保留4位小数
    }))
  },
  set: (value) => {
    // 将百分比转换回小数存储到 adminStore
    const convertedValue = value.map(prize => ({
      ...prize,
      probability: parseFloat((prize.probability / 100).toFixed(6)) // 转换为小数，保留6位小数
    }))
    adminStore.prizeConfig = convertedValue
  }
})

// 系统设置 - 直接使用 adminStore 的数据
const systemSettings = computed({
  get: () => adminStore.systemSettings || {},
  set: (value) => {
    adminStore.systemSettings = value
  }
})

// 横幅设置 - 使用 adminStore 的数据
const banners = computed({
  get: () => adminStore.banners || [],
  set: (value) => {
    adminStore.banners = value
  }
})

// 计算总概率
const totalProbability = computed(() => {
  if (!prizeConfig.value || prizeConfig.value.length === 0) return 0
  return prizeConfig.value.reduce((sum, prize) => sum + (parseFloat(prize.probability) || 0), 0)
})

// 更新概率值（实时更新）
const updateProbability = (index: number, event: Event) => {
  const target = event.target as HTMLInputElement
  const newValue = parseFloat(target.value) || 0

  // 创建新的配置数组来触发响应式更新
  const newConfig = [...prizeConfig.value]
  newConfig[index] = {
    ...newConfig[index],
    probability: newValue
  }
  prizeConfig.value = newConfig
}

// 获取奖品等级颜色
const getPrizeColor = (level: number) => {
  if (level <= 3) return 'bg-red-500/20 border-red-500/30 text-red-400'
  if (level <= 6) return 'bg-yellow-500/20 border-yellow-500/30 text-yellow-400'
  if (level <= 9) return 'bg-green-500/20 border-green-500/30 text-green-400'
  return 'bg-gray-500/20 border-gray-500/30 text-gray-400'
}

// 刷新配置
const handleRefresh = async () => {
  try {
    const result = await adminStore.fetchSettings()
    if (result.success) {
      ElMessage.success('配置已刷新')
    } else {
      ElMessage.error(result.message || '刷新失败')
    }
  } catch (error) {
    ElMessage.error('刷新失败')
  }
}

// 保存奖品配置
const savePrizeConfig = async () => {
  if (prizeConfig.value.length === 0) {
    ElMessage.warning('请先配置奖品信息')
    return
  }

  if (Math.abs(totalProbability.value - 100) > 0.01) {
    ElMessage.warning('总概率必须等于100%')
    return
  }

  try {
    const result = await adminStore.updateSettings({
      prizeConfig: prizeConfig.value
    })
    if (result.success) {
      ElMessage.success('奖品配置保存成功')
    } else {
      ElMessage.error(result.message || '保存失败')
    }
  } catch (error) {
    ElMessage.error('保存失败')
  }
}

// 保存系统设置
const saveSystemSettings = async () => {
  if (!systemSettings.value || Object.keys(systemSettings.value).length === 0) {
    ElMessage.warning('请先配置系统设置信息')
    return
  }

  try {
    const result = await adminStore.updateSettings({
      systemSettings: systemSettings.value
    })
    if (result.success) {
      ElMessage.success('系统设置保存成功')
    } else {
      ElMessage.error(result.message || '保存失败')
    }
  } catch (error) {
    ElMessage.error('保存失败')
  }
}

// 添加横幅
const addBanner = () => {
  const newBanners = [...banners.value]
  newBanners.push({
    title: '',
    url: '',
    imageUrl: '',
    enabled: true,
    sort: newBanners.length + 1,
    description: ''
  })
  banners.value = newBanners
}

// 删除横幅
const removeBanner = async (index: number) => {
  try {
    await ElMessageBox.confirm(
      '确定要删除这个横幅吗？',
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )

    const newBanners = [...banners.value]
    newBanners.splice(index, 1)

    // 立即保存到后端
    const result = await adminStore.updateSettings({
      banners: newBanners
    })

    if (result.success) {
      banners.value = newBanners
      ElMessage.success('横幅删除成功')
    } else {
      ElMessage.error(result.message || '删除失败')
    }
  } catch (error) {
    // 用户取消删除或删除失败
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

// 保存横幅设置
const saveBanners = async () => {
  try {
    const result = await adminStore.updateSettings({
      banners: banners.value
    })
    if (result.success) {
      ElMessage.success('横幅设置保存成功')
    } else {
      ElMessage.error(result.message || '保存失败')
    }
  } catch (error) {
    ElMessage.error('保存失败')
  }
}

// 防抖保存横幅（用于实时保存）
let bannerSaveTimeout: number | null = null
const debouncedSaveBanners = () => {
  if (bannerSaveTimeout) {
    clearTimeout(bannerSaveTimeout)
  }
  bannerSaveTimeout = setTimeout(async () => {
    try {
      await adminStore.updateSettings({
        banners: banners.value
      })
    } catch (error) {
      console.error('自动保存横幅失败:', error)
    }
  }, 1000) // 1秒后自动保存
}

// 清理过期数据
const cleanExpiredData = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要清理过期数据吗？此操作将删除30天前的过期日志和临时数据，不可恢复。',
      '警告',
      {
        confirmButtonText: '确定清理',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )

    const loadingMessage = ElMessage({
      message: '正在清理过期数据...',
      type: 'info',
      duration: 0
    })

    try {
      const response = await adminAPI.cleanExpiredData()
      loadingMessage.close()

      if (response.data.success) {
        ElMessage.success(response.data.message || '过期数据清理完成')
      } else {
        ElMessage.error(response.data.error || '清理失败')
      }
    } catch (error: any) {
      loadingMessage.close()
      ElMessage.error(error.response?.data?.error || '清理失败')
    }
  } catch {
    // 用户取消
  }
}

// 导出用户数据
const exportUserData = async () => {
  const loadingMessage = ElMessage({
    message: '正在导出用户数据...',
    type: 'info',
    duration: 0
  })

  try {
    const response = await adminAPI.exportUserData()
    loadingMessage.close()

    if (response.data.success) {
      // 创建Excel文件
      const worksheet = XLSX.utils.json_to_sheet(response.data.data)
      const workbook = XLSX.utils.book_new()
      XLSX.utils.book_append_sheet(workbook, worksheet, '用户数据')

      // 下载文件
      const fileName = `用户数据_${new Date().toISOString().split('T')[0]}.xlsx`
      XLSX.writeFile(workbook, fileName)

      ElMessage.success('用户数据导出成功')
    } else {
      ElMessage.error(response.data.error || '导出失败')
    }
  } catch (error: any) {
    loadingMessage.close()
    ElMessage.error(error.response?.data?.error || '导出失败')
  }
}

// 导出抽奖记录
const exportLotteryData = async () => {
  const loadingMessage = ElMessage({
    message: '正在导出抽奖记录...',
    type: 'info',
    duration: 0
  })

  try {
    const response = await adminAPI.exportLotteryData()
    loadingMessage.close()

    if (response.data.success) {
      // 创建Excel文件
      const worksheet = XLSX.utils.json_to_sheet(response.data.data)
      const workbook = XLSX.utils.book_new()
      XLSX.utils.book_append_sheet(workbook, worksheet, '抽奖记录')

      // 下载文件
      const fileName = `抽奖记录_${new Date().toISOString().split('T')[0]}.xlsx`
      XLSX.writeFile(workbook, fileName)

      ElMessage.success('抽奖记录导出成功')
    } else {
      ElMessage.error(response.data.error || '导出失败')
    }
  } catch (error: any) {
    loadingMessage.close()
    ElMessage.error(error.response?.data?.error || '导出失败')
  }
}

// 重置所有数据
const resetAllData = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要重置所有数据吗？此操作将删除所有用户和抽奖记录，不可恢复！',
      '危险操作',
      {
        confirmButtonText: '确定重置',
        cancelButtonText: '取消',
        type: 'error',
      }
    )

    // 二次确认
    await ElMessageBox.confirm(
      '请再次确认：这将永久删除所有用户数据、抽奖记录等信息，此操作无法撤销！',
      '最终确认',
      {
        confirmButtonText: '我确定要重置',
        cancelButtonText: '取消',
        type: 'error',
      }
    )

    const loadingMessage = ElMessage({
      message: '正在重置数据...',
      type: 'warning',
      duration: 0
    })

    try {
      const response = await adminAPI.resetAllData()
      loadingMessage.close()

      if (response.data.success) {
        ElMessage.success('数据重置完成')
        // 重新加载页面数据
        await adminStore.fetchSettings()
      } else {
        ElMessage.error(response.data.error || '重置失败')
      }
    } catch (error: any) {
      loadingMessage.close()
      ElMessage.error(error.response?.data?.error || '重置失败')
    }
  } catch {
    // 用户取消
  }
}

onMounted(async () => {
  // 加载配置数据
  await adminStore.fetchSettings()
})
</script>
