const { DataTypes } = require('sequelize')
const { sequelize } = require('../config/database')

// 管理员操作日志模型
const AdminLog = sequelize.define('AdminLog', {
  id: {
    type: DataTypes.BIGINT,
    primaryKey: true,
    autoIncrement: true
  },
  adminId: {
    type: DataTypes.BIGINT,
    allowNull: false,
    field: 'admin_id'
  },
  action: {
    type: DataTypes.STRING(50),
    allowNull: false,
    comment: '操作类型'
  },
  description: {
    type: DataTypes.TEXT,
    allowNull: false,
    comment: '操作描述'
  },
  riskLevel: {
    type: DataTypes.ENUM('低', '中', '高'),
    allowNull: false,
    defaultValue: '低',
    field: 'risk_level',
    comment: '风险等级'
  },
  ipAddress: {
    type: DataTypes.STRING(45),
    allowNull: true,
    field: 'ip_address',
    comment: 'IP地址'
  },
  userAgent: {
    type: DataTypes.TEXT,
    allowNull: true,
    field: 'user_agent',
    comment: '用户代理'
  },
  details: {
    type: DataTypes.JSON,
    allowNull: true,
    comment: '详细信息(JSON格式)'
  }
}, {
  tableName: 'admin_logs',
  underscored: true,
  indexes: [
    {
      fields: ['admin_id']
    },
    {
      fields: ['action']
    },
    {
      fields: ['risk_level']
    },
    {
      fields: ['created_at']
    },
    {
      fields: ['admin_id', 'action']
    }
  ]
})

module.exports = AdminLog
