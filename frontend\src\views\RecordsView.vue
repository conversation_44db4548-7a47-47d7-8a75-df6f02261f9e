<template>
  <div class="min-h-screen bg-black text-white">
    <!-- 使用公共头部组件 -->
    <AppHeader />

    <!-- 主要内容区域 -->
    <main class="container mx-auto px-3 md:px-4 py-4 md:py-6">
      <!-- 页面标题 -->
      <section class="mb-6 md:mb-8">
        <div class="text-center">
          <h1 class="text-2xl md:text-4xl font-bold text-gold-400 mb-2 md:mb-4">
            <span class="mr-2 md:mr-3">📊</span>中奖记录
          </h1>
          <p class="text-gray-400 text-sm md:text-lg">查看您的抽奖历史和中奖记录</p>
        </div>
      </section>

      <!-- 统计卡片 -->
      <section class="mb-6 md:mb-8">
        <div class="grid grid-cols-2 md:grid-cols-4 gap-3 md:gap-4">
          <!-- 总抽奖次数 -->
          <div class="bg-gradient-to-br from-blue-600 to-blue-700 rounded-lg md:rounded-xl p-3 md:p-6 text-center">
            <div class="text-lg md:text-3xl mb-1 md:mb-2">🎲</div>
            <div class="text-lg md:text-2xl font-bold text-white">{{ userStats.totalDraws }}</div>
            <div class="text-xs md:text-sm text-blue-100">总抽奖次数</div>
          </div>

          <!-- 中奖次数 -->
          <div class="bg-gradient-to-br from-green-600 to-green-700 rounded-lg md:rounded-xl p-3 md:p-6 text-center">
            <div class="text-lg md:text-3xl mb-1 md:mb-2">🏆</div>
            <div class="text-lg md:text-2xl font-bold text-white">{{ userStats.totalWins }}</div>
            <div class="text-xs md:text-sm text-green-100">中奖次数</div>
          </div>

          <!-- 中奖率 -->
          <div class="bg-gradient-to-br from-purple-600 to-purple-700 rounded-lg md:rounded-xl p-3 md:p-6 text-center">
            <div class="text-lg md:text-3xl mb-1 md:mb-2">📈</div>
            <div class="text-lg md:text-2xl font-bold text-white">{{ winRate }}%</div>
            <div class="text-xs md:text-sm text-purple-100">中奖率</div>
          </div>

          <!-- 累计奖金 -->
          <div class="bg-gradient-to-br from-gold-600 to-gold-700 rounded-lg md:rounded-xl p-3 md:p-6 text-center">
            <div class="text-lg md:text-3xl mb-1 md:mb-2">💰</div>
            <div class="text-lg md:text-2xl font-bold text-white">¥{{ userStats.totalPrizeValue }}</div>
            <div class="text-xs md:text-sm text-gold-100">累计奖金</div>
          </div>
        </div>
      </section>

      <!-- 筛选和搜索 -->
      <section class="mb-6">
        <div class="bg-gradient-to-br from-gray-800 to-gray-900 rounded-xl p-3 md:p-6 border border-gold-500/30">
          <div class="flex flex-col md:flex-row gap-3 md:gap-4 md:items-center">
            <!-- 时间筛选 -->
            <div class="flex items-center space-x-2 w-full md:w-auto">
              <span class="text-gray-300 text-xs md:text-sm whitespace-nowrap">时间:</span>
              <select v-model="timeFilter" class="flex-1 md:flex-none bg-gray-700 text-white rounded-lg px-2 md:px-3 py-2 text-xs md:text-sm border border-gray-600 focus:border-gold-500">
                <option value="all">全部</option>
                <option value="today">今天</option>
                <option value="week">本周</option>
                <option value="month">本月</option>
              </select>
            </div>

            <!-- 状态筛选 -->
            <div class="flex items-center space-x-2 w-full md:w-auto">
              <span class="text-gray-300 text-xs md:text-sm whitespace-nowrap">状态:</span>
              <select v-model="statusFilter" class="flex-1 md:flex-none bg-gray-700 text-white rounded-lg px-2 md:px-3 py-2 text-xs md:text-sm border border-gray-600 focus:border-gold-500">
                <option value="all">全部</option>
                <option value="win">中奖</option>
                <option value="lose">未中奖</option>
              </select>
            </div>

            <!-- 刷新按钮 -->
            <button @click="refreshRecords" class="w-full md:w-auto md:ml-auto px-3 md:px-4 py-2 bg-gold-600 hover:bg-gold-500 text-black font-semibold rounded-lg transition-colors text-xs md:text-sm">
              <span class="mr-2">🔄</span>刷新
            </button>
          </div>
        </div>
      </section>

      <!-- 记录列表 -->
      <section class="mb-6 md:mb-8">
        <!-- 桌面端表格布局 -->
        <div class="hidden md:block bg-gradient-to-br from-gray-800 to-gray-900 rounded-xl border border-gold-500/30 overflow-hidden">
          <!-- 表头 -->
          <div class="bg-gray-700/50 px-6 py-4 border-b border-gray-600">
            <div class="grid grid-cols-5 gap-4 text-sm font-semibold text-gold-400">
              <div>时间</div>
              <div>期数</div>
              <div>奖品</div>
              <div>状态</div>
              <div>奖金</div>
            </div>
          </div>

          <!-- 记录列表 -->
          <div class="divide-y divide-gray-700">
            <div v-if="filteredRecords.length === 0" class="px-6 py-12 text-center">
              <div class="text-6xl mb-4 opacity-50">📝</div>
              <p class="text-gray-400 text-lg">暂无抽奖记录</p>
              <router-link to="/lottery" class="inline-block mt-4 px-6 py-2 bg-gold-600 hover:bg-gold-500 text-black font-semibold rounded-lg transition-colors">
                去抽奖
              </router-link>
            </div>

            <div
              v-for="record in paginatedRecords"
              :key="record.id"
              class="px-6 py-4 hover:bg-gray-700/30 transition-colors"
            >
              <div class="grid grid-cols-5 gap-4 items-center text-sm">
                <!-- 时间 -->
                <div class="text-gray-300">
                  <div>{{ formatDate(record.drawTime) }}</div>
                  <div class="text-xs text-gray-500">{{ formatTime(record.drawTime) }}</div>
                </div>

                <!-- 期数 -->
                <div class="text-white font-medium">{{ record.periodName }}</div>

                <!-- 奖品 -->
                <div class="text-white">{{ record.prizeName }}</div>

                <!-- 状态 -->
                <div>
                  <span v-if="record.isWin" class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-600/20 text-green-400 border border-green-500/30">
                    🏆 中奖
                  </span>
                  <span v-else class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-600/20 text-gray-400 border border-gray-500/30">
                    😔 未中奖
                  </span>
                </div>

                <!-- 奖金 -->
                <div>
                  <span v-if="record.isWin" class="text-green-400 font-bold">¥{{ record.prizeValue }}</span>
                  <span v-else class="text-gray-500">-</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 移动端卡片布局 -->
        <div class="md:hidden space-y-3">
          <div v-if="filteredRecords.length === 0" class="bg-gradient-to-br from-gray-800 to-gray-900 rounded-lg border border-gold-500/30 p-8 text-center">
            <div class="text-4xl mb-3 opacity-50">📝</div>
            <p class="text-gray-400 text-sm mb-4">暂无抽奖记录</p>
            <router-link to="/lottery" class="inline-block px-4 py-2 bg-gold-600 hover:bg-gold-500 text-black font-semibold rounded-lg transition-colors text-sm">
              去抽奖
            </router-link>
          </div>

          <div
            v-for="record in paginatedRecords"
            :key="record.id"
            class="bg-gradient-to-br from-gray-800 to-gray-900 rounded-lg border border-gold-500/30 p-4"
          >
            <!-- 卡片头部 -->
            <div class="flex items-center justify-between mb-3">
              <div class="flex items-center space-x-2">
                <span class="text-gold-400 font-medium text-sm">{{ record.periodName }}</span>
                <span v-if="record.isWin" class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-600/20 text-green-400 border border-green-500/30">
                  🏆 中奖
                </span>
                <span v-else class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-600/20 text-gray-400 border border-gray-500/30">
                  😔 未中奖
                </span>
              </div>
              <div class="text-xs text-gray-400">
                {{ formatDate(record.drawTime) }} {{ formatTime(record.drawTime) }}
              </div>
            </div>

            <!-- 卡片内容 -->
            <div class="flex items-center justify-between">
              <div>
                <div class="text-white text-sm font-medium mb-1">{{ record.prizeName }}</div>
                <div class="text-xs text-gray-400">奖品名称</div>
              </div>
              <div class="text-right">
                <div v-if="record.isWin" class="text-green-400 font-bold text-sm">¥{{ record.prizeValue }}</div>
                <div v-else class="text-gray-500 text-sm">-</div>
                <div class="text-xs text-gray-400">奖金</div>
              </div>
            </div>
          </div>
        </div>
      </section>

      <!-- 分页 -->
      <section v-if="totalPages > 1" class="flex flex-col md:flex-row justify-center md:items-center space-y-3 md:space-y-0">
        <div class="flex items-center justify-center space-x-1 md:space-x-2">
          <!-- 上一页 -->
          <button
            @click="goToPage(currentPage - 1)"
            :disabled="currentPage === 1"
            class="px-2 md:px-3 py-2 text-xs md:text-sm bg-gray-700 hover:bg-gray-600 disabled:opacity-50 disabled:cursor-not-allowed text-white rounded-lg transition-colors"
          >
            上一页
          </button>

          <!-- 页码按钮 -->
          <div class="flex items-center space-x-1">
            <!-- 第一页 -->
            <button
              v-if="currentPage > 3"
              @click="goToPage(1)"
              class="px-2 md:px-3 py-2 text-xs md:text-sm bg-gray-700 hover:bg-gray-600 text-white rounded-lg transition-colors"
            >
              1
            </button>

            <!-- 省略号 -->
            <span v-if="currentPage > 4" class="px-1 md:px-2 text-gray-400 text-xs md:text-sm">...</span>

            <!-- 当前页前后的页码 -->
            <button
              v-for="page in visiblePages"
              :key="page"
              @click="goToPage(page)"
              :class="[
                'px-2 md:px-3 py-2 text-xs md:text-sm rounded-lg transition-colors',
                page === currentPage
                  ? 'bg-gold-600 text-black font-bold'
                  : 'bg-gray-700 hover:bg-gray-600 text-white'
              ]"
            >
              {{ page }}
            </button>

            <!-- 省略号 -->
            <span v-if="currentPage < totalPages - 3" class="px-1 md:px-2 text-gray-400 text-xs md:text-sm">...</span>

            <!-- 最后一页 -->
            <button
              v-if="currentPage < totalPages - 2"
              @click="goToPage(totalPages)"
              class="px-2 md:px-3 py-2 text-xs md:text-sm bg-gray-700 hover:bg-gray-600 text-white rounded-lg transition-colors"
            >
              {{ totalPages }}
            </button>
          </div>

          <!-- 下一页 -->
          <button
            @click="goToPage(currentPage + 1)"
            :disabled="currentPage === totalPages"
            class="px-2 md:px-3 py-2 text-xs md:text-sm bg-gray-700 hover:bg-gray-600 disabled:opacity-50 disabled:cursor-not-allowed text-white rounded-lg transition-colors"
          >
            下一页
          </button>
        </div>

        <!-- 页面信息 -->
        <div class="md:ml-4 text-xs md:text-sm text-gray-400 text-center md:text-left">
          共 {{ filteredRecords.length }} 条记录，第 {{ currentPage }} / {{ totalPages }} 页
        </div>
      </section>
    </main>

    <!-- 使用公共页脚组件 -->
    <AppFooter />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '../stores/auth'
import AppHeader from '@/components/AppHeader.vue'
import AppFooter from '@/components/AppFooter.vue'

const router = useRouter()
const authStore = useAuthStore()

// 响应式数据
const timeFilter = ref('all')
const statusFilter = ref('all')
const currentPage = ref(1)
const pageSize = 10

// 模拟用户统计数据
const userStats = ref({
  totalDraws: 25,
  totalWins: 8,
  totalPrizeValue: 1580
})

// 计算中奖率
const winRate = computed(() => {
  if (userStats.value.totalDraws === 0) return 0
  return ((userStats.value.totalWins / userStats.value.totalDraws) * 100).toFixed(1)
})

// 模拟抽奖记录数据
const records = ref([
  {
    id: 1,
    drawTime: new Date('2024-01-15 14:30:00'),
    periodName: '第15期',
    prizeName: '一等奖',
    prizeValue: 1000,
    isWin: true
  },
  {
    id: 2,
    drawTime: new Date('2024-01-15 10:15:00'),
    periodName: '第14期',
    prizeName: '谢谢参与',
    prizeValue: 0,
    isWin: false
  },
  {
    id: 3,
    drawTime: new Date('2024-01-14 16:45:00'),
    periodName: '第13期',
    prizeName: '三等奖',
    prizeValue: 100,
    isWin: true
  },
  {
    id: 4,
    drawTime: new Date('2024-01-14 09:20:00'),
    periodName: '第12期',
    prizeName: '二等奖',
    prizeValue: 500,
    isWin: true
  },
  {
    id: 5,
    drawTime: new Date('2024-01-13 15:10:00'),
    periodName: '第11期',
    prizeName: '谢谢参与',
    prizeValue: 0,
    isWin: false
  },
  {
    id: 6,
    drawTime: new Date('2024-01-13 11:30:00'),
    periodName: '第10期',
    prizeName: '四等奖',
    prizeValue: 50,
    isWin: true
  },
  {
    id: 7,
    drawTime: new Date('2024-01-12 16:20:00'),
    periodName: '第9期',
    prizeName: '谢谢参与',
    prizeValue: 0,
    isWin: false
  },
  {
    id: 8,
    drawTime: new Date('2024-01-12 14:15:00'),
    periodName: '第8期',
    prizeName: '五等奖',
    prizeValue: 10,
    isWin: true
  },
  {
    id: 9,
    drawTime: new Date('2024-01-12 09:45:00'),
    periodName: '第7期',
    prizeName: '谢谢参与',
    prizeValue: 0,
    isWin: false
  },
  {
    id: 10,
    drawTime: new Date('2024-01-11 17:30:00'),
    periodName: '第6期',
    prizeName: '三等奖',
    prizeValue: 100,
    isWin: true
  },
  {
    id: 11,
    drawTime: new Date('2024-01-11 13:20:00'),
    periodName: '第5期',
    prizeName: '谢谢参与',
    prizeValue: 0,
    isWin: false
  },
  {
    id: 12,
    drawTime: new Date('2024-01-11 10:10:00'),
    periodName: '第4期',
    prizeName: '四等奖',
    prizeValue: 50,
    isWin: true
  },
  {
    id: 13,
    drawTime: new Date('2024-01-10 15:45:00'),
    periodName: '第3期',
    prizeName: '谢谢参与',
    prizeValue: 0,
    isWin: false
  },
  {
    id: 14,
    drawTime: new Date('2024-01-10 12:30:00'),
    periodName: '第2期',
    prizeName: '二等奖',
    prizeValue: 500,
    isWin: true
  },
  {
    id: 15,
    drawTime: new Date('2024-01-10 09:15:00'),
    periodName: '第1期',
    prizeName: '谢谢参与',
    prizeValue: 0,
    isWin: false
  }
])

// 筛选后的记录
const filteredRecords = computed(() => {
  let filtered = records.value

  // 时间筛选
  if (timeFilter.value !== 'all') {
    const now = new Date()
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate())

    filtered = filtered.filter(record => {
      const recordDate = new Date(record.drawTime)

      switch (timeFilter.value) {
        case 'today':
          return recordDate >= today
        case 'week':
          const weekAgo = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000)
          return recordDate >= weekAgo
        case 'month':
          const monthAgo = new Date(today.getFullYear(), today.getMonth() - 1, today.getDate())
          return recordDate >= monthAgo
        default:
          return true
      }
    })
  }

  // 状态筛选
  if (statusFilter.value !== 'all') {
    filtered = filtered.filter(record => {
      return statusFilter.value === 'win' ? record.isWin : !record.isWin
    })
  }

  return filtered
})

// 分页后的记录
const paginatedRecords = computed(() => {
  const start = (currentPage.value - 1) * pageSize
  const end = start + pageSize
  return filteredRecords.value.slice(start, end)
})

// 总页数
const totalPages = computed(() => {
  return Math.ceil(filteredRecords.value.length / pageSize)
})

// 可见的页码
const visiblePages = computed(() => {
  const pages = []
  const start = Math.max(1, currentPage.value - 2)
  const end = Math.min(totalPages.value, currentPage.value + 2)

  for (let i = start; i <= end; i++) {
    pages.push(i)
  }

  return pages
})

// 跳转到指定页面
const goToPage = (page: number) => {
  if (page >= 1 && page <= totalPages.value) {
    currentPage.value = page
  }
}

// 格式化日期
const formatDate = (date: Date) => {
  return date.toLocaleDateString('zh-CN', {
    month: '2-digit',
    day: '2-digit'
  })
}

// 格式化时间
const formatTime = (date: Date) => {
  return date.toLocaleTimeString('zh-CN', {
    hour: '2-digit',
    minute: '2-digit'
  })
}

// 刷新记录
const refreshRecords = () => {
  // 这里可以调用API刷新数据
  console.log('刷新记录')
}



// 检查登录状态
const checkLoginStatus = () => {
  if (!authStore.isAuthenticated) {
    router.push('/login')
  }
}

// 监听筛选条件变化，重置页码
watch([timeFilter, statusFilter], () => {
  currentPage.value = 1
})

onMounted(() => {
  checkLoginStatus()
})
</script>

<style scoped>
/* 自定义样式 */
</style>
