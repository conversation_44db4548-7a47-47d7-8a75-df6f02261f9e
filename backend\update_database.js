const { Sequelize } = require('sequelize')
require('dotenv').config()

// 创建数据库连接
const sequelize = new Sequelize(
  process.env.DB_NAME || 'as_chou',
  process.env.DB_USER || 'root',
  process.env.DB_PASSWORD || '123456',
  {
    host: process.env.DB_HOST || 'localhost',
    port: process.env.DB_PORT || 3306,
    dialect: 'mysql',
    logging: console.log
  }
)

async function updateDatabase() {
  try {
    console.log('🔄 开始更新数据库结构...')
    
    // 测试连接
    await sequelize.authenticate()
    console.log('✅ 数据库连接成功')
    
    // 添加新字段
    const queries = [
      `ALTER TABLE lottery_periods 
       ADD COLUMN total_prize_amount DECIMAL(15,2) DEFAULT 0.00 COMMENT '本期抽奖总金额' AFTER total_attempts`,
      
      `ALTER TABLE lottery_periods 
       ADD COLUMN actual_drawable_amount DECIMAL(15,2) DEFAULT 0.00 COMMENT '实际可抽取金额' AFTER total_prize_amount`,
      
      `ALTER TABLE lottery_periods 
       ADD COLUMN total_shares INT DEFAULT 0 COMMENT '总份数' AFTER actual_drawable_amount`
    ]
    
    for (const query of queries) {
      try {
        await sequelize.query(query)
        console.log('✅ 执行成功:', query.split('\n')[0])
      } catch (error) {
        if (error.original && error.original.code === 'ER_DUP_FIELDNAME') {
          console.log('⚠️  字段已存在，跳过:', query.split('\n')[0])
        } else {
          console.error('❌ 执行失败:', query.split('\n')[0])
          console.error('错误:', error.message)
        }
      }
    }
    
    // 更新现有数据
    try {
      await sequelize.query(`
        UPDATE lottery_periods 
        SET 
          total_prize_amount = 0.00,
          actual_drawable_amount = 0.00,
          total_shares = 0
        WHERE 
          total_prize_amount IS NULL 
          OR actual_drawable_amount IS NULL 
          OR total_shares IS NULL
      `)
      console.log('✅ 更新现有数据完成')
    } catch (error) {
      console.log('⚠️  更新现有数据失败:', error.message)
    }
    
    // 验证表结构
    const [results] = await sequelize.query('DESCRIBE lottery_periods')
    console.log('📋 当前表结构:')
    results.forEach(field => {
      console.log(`  ${field.Field}: ${field.Type} ${field.Null === 'YES' ? 'NULL' : 'NOT NULL'} ${field.Default ? `DEFAULT ${field.Default}` : ''}`)
    })
    
    console.log('🎉 数据库更新完成!')
    
  } catch (error) {
    console.error('❌ 数据库更新失败:', error)
  } finally {
    await sequelize.close()
  }
}

// 执行更新
updateDatabase()
