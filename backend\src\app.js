const express = require('express')
const cors = require('cors')
require('dotenv').config()

// 导入配置和工具
const { testConnection } = require('./config/database')

// 导入路由
const authRoutes = require('./routes/auth')
const lotteryRoutes = require('./routes/lottery')
const adminAuthRoutes = require('./routes/admin/auth')
const adminRoutes = require('./routes/admin/admin')

const app = express()
const PORT = process.env.PORT || 3000

// 中间件配置
app.use(cors({
  origin: [
    'http://localhost:5173',
    'http://localhost:5174',
    process.env.FRONTEND_URL
  ].filter(Boolean),
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization']
}))
app.use(express.json({ limit: '10mb' }))
app.use(express.urlencoded({ extended: true, limit: '10mb' }))

// 请求日志中间件
app.use((req, res, next) => {
  console.log(`${new Date().toISOString()} - ${req.method} ${req.path}`)
  next()
})

// 健康检查接口
app.get('/health', (req, res) => {
  res.json({
    success: true,
    message: '服务器运行正常',
    timestamp: new Date().toISOString(),
    version: '2.0.0'
  })
})

// API路由配置
app.use('/api/auth', authRoutes)                    // 前台用户认证
app.use('/api/lottery', lotteryRoutes)              // 抽奖相关接口
app.use('/api/admin/auth', adminAuthRoutes)         // 后台管理员认证
app.use('/api/admin', adminRoutes)                  // 后台管理功能

// 404处理
app.use((req, res) => {
  res.status(404).json({
    success: false,
    error: '接口不存在',
    path: req.originalUrl
  })
})

// 全局错误处理中间件
app.use((err, req, res, next) => {
  console.error('服务器错误:', err)
  
  res.status(500).json({
    success: false,
    error: '服务器内部错误',
    message: process.env.NODE_ENV === 'development' ? err.message : '请稍后重试'
  })
})

// 启动服务器
const startServer = async () => {
  try {
    console.log('🚀 正在启动抽奖平台后端服务器...')

    // 1. 测试数据库连接
    const dbConnected = await testConnection()
    if (!dbConnected) {
      throw new Error('数据库连接失败')
    }

    // 2. 启动HTTP服务器
    app.listen(PORT, () => {
      console.log('🎰 抽奖平台后端服务器启动成功!')
      console.log(`📡 服务器地址: http://localhost:${PORT}`)
      console.log(`🔗 健康检查: http://localhost:${PORT}/health`)
      console.log(`👤 用户API: http://localhost:${PORT}/api/auth`)
      console.log(`🔐 管理员API: http://localhost:${PORT}/api/admin/auth`)
      console.log('=' .repeat(50))
    })

  } catch (error) {
    console.error('❌ 启动服务器失败:', error)
    process.exit(1)
  }
}

// 优雅关闭处理
process.on('SIGTERM', () => {
  console.log('🔄 收到SIGTERM信号，正在优雅关闭服务器...')
  process.exit(0)
})

process.on('SIGINT', () => {
  console.log('🔄 收到SIGINT信号，正在优雅关闭服务器...')
  process.exit(0)
})

// 未捕获异常处理
process.on('uncaughtException', (err) => {
  console.error('❌ 未捕获的异常:', err)
  process.exit(1)
})

process.on('unhandledRejection', (reason, promise) => {
  console.error('❌ 未处理的Promise拒绝:', reason)
  process.exit(1)
})

// 启动服务器
startServer()

module.exports = app
