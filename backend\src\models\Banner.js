const { DataTypes } = require('sequelize')
const { sequelize } = require('../config/database')

const Banner = sequelize.define('Banner', {
  id: {
    type: DataTypes.BIGINT,
    primaryKey: true,
    autoIncrement: true
  },
  title: {
    type: DataTypes.STRING(100),
    allowNull: false
  },
  url: {
    type: DataTypes.STRING(500),
    allowNull: true
  },
  image_url: {
    type: DataTypes.STRING(500),
    allowNull: true,
    field: 'image_url'
  },
  enabled: {
    type: DataTypes.BOOLEAN,
    defaultValue: true
  },
  sort_order: {
    type: DataTypes.INTEGER,
    defaultValue: 0,
    field: 'sort_order'
  },
  description: {
    type: DataTypes.STRING(255),
    allowNull: true
  },
  start_time: {
    type: DataTypes.DATE,
    allowNull: true,
    field: 'start_time'
  },
  end_time: {
    type: DataTypes.DATE,
    allowNull: true,
    field: 'end_time'
  },
  click_count: {
    type: DataTypes.INTEGER,
    defaultValue: 0,
    field: 'click_count'
  },
  is_active: {
    type: DataTypes.BOOLEAN,
    defaultValue: true,
    field: 'is_active'
  },
  created_at: {
    type: DataTypes.DATE,
    allowNull: false,
    field: 'created_at'
  },
  updated_at: {
    type: DataTypes.DATE,
    allowNull: false,
    field: 'updated_at'
  }
}, {
  tableName: 'banners',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at'
})

// 获取有效的横幅列表
Banner.getActiveBanners = async function() {
  try {
    const now = new Date()
    
    return await this.findAll({
      where: {
        is_active: true,
        enabled: true
      },
      order: [['sort_order', 'ASC'], ['created_at', 'DESC']],
      attributes: [
        'id', 'title', 'url', 'image_url', 'enabled', 
        'sort_order', 'description', 'start_time', 'end_time'
      ]
    })
  } catch (error) {
    console.error('Error getting active banners:', error)
    return []
  }
}

// 增加点击次数
Banner.incrementClick = async function(id) {
  try {
    await this.increment('click_count', { where: { id } })
  } catch (error) {
    console.error('Error incrementing click count:', error)
  }
}

module.exports = Banner
