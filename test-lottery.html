<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>抽奖API测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .result {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 5px;
            margin-top: 10px;
            white-space: pre-wrap;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
        }
        .success {
            background: #d4edda;
            color: #155724;
        }
    </style>
</head>
<body>
    <h1>抽奖API测试页面</h1>
    
    <div class="test-section">
        <h3>1. 获取当前期次</h3>
        <button onclick="testCurrentPeriod()">测试当前期次API</button>
        <div id="currentPeriodResult" class="result"></div>
    </div>

    <div class="test-section">
        <h3>2. 用户登录</h3>
        <input type="text" id="username" placeholder="用户名" value="aa123456">
        <input type="password" id="password" placeholder="密码" value="123456">
        <button onclick="testLogin()">登录</button>
        <div id="loginResult" class="result"></div>
    </div>

    <div class="test-section">
        <h3>3. 获取用户状态</h3>
        <button onclick="testUserStatus()">测试用户状态API</button>
        <div id="userStatusResult" class="result"></div>
    </div>

    <div class="test-section">
        <h3>4. 抽奖测试</h3>
        <button onclick="testLotteryDraw()">开始抽奖</button>
        <div id="lotteryResult" class="result"></div>
    </div>

    <script>
        let currentToken = null;
        let currentPeriodId = null;

        async function testCurrentPeriod() {
            try {
                const response = await fetch('http://localhost:3000/api/lottery/current-period');
                const data = await response.json();
                
                if (data.success) {
                    currentPeriodId = data.data.period.id;
                    document.getElementById('currentPeriodResult').className = 'result success';
                    document.getElementById('currentPeriodResult').textContent = 
                        `✅ 成功获取当前期次\n期次ID: ${data.data.period.id}\n期次名称: ${data.data.period.periodName}\n状态: ${data.data.period.status}\n奖品数量: ${data.data.period.prizes.length}`;
                } else {
                    throw new Error(data.error || '获取失败');
                }
            } catch (error) {
                document.getElementById('currentPeriodResult').className = 'result error';
                document.getElementById('currentPeriodResult').textContent = `❌ 错误: ${error.message}`;
            }
        }

        async function testLogin() {
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            
            try {
                const response = await fetch('http://localhost:3000/api/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ username, password })
                });
                
                const data = await response.json();
                
                if (data.success) {
                    currentToken = data.data.token;
                    document.getElementById('loginResult').className = 'result success';
                    document.getElementById('loginResult').textContent = 
                        `✅ 登录成功\n用户ID: ${data.data.user.id}\n用户名: ${data.data.user.username}\nToken: ${currentToken.substring(0, 50)}...`;
                } else {
                    throw new Error(data.error || '登录失败');
                }
            } catch (error) {
                document.getElementById('loginResult').className = 'result error';
                document.getElementById('loginResult').textContent = `❌ 错误: ${error.message}`;
            }
        }

        async function testUserStatus() {
            if (!currentToken) {
                document.getElementById('userStatusResult').className = 'result error';
                document.getElementById('userStatusResult').textContent = '❌ 请先登录';
                return;
            }

            try {
                const response = await fetch('http://localhost:3000/api/lottery/user-status', {
                    headers: {
                        'Authorization': `Bearer ${currentToken}`
                    }
                });
                
                const data = await response.json();
                
                if (data.success) {
                    document.getElementById('userStatusResult').className = 'result success';
                    document.getElementById('userStatusResult').textContent = 
                        `✅ 用户状态获取成功\n当前期次: ${data.data.currentPeriod?.periodName || '无'}\n剩余抽奖次数: ${data.data.remainingAttempts}`;
                } else {
                    throw new Error(data.error || '获取失败');
                }
            } catch (error) {
                document.getElementById('userStatusResult').className = 'result error';
                document.getElementById('userStatusResult').textContent = `❌ 错误: ${error.message}`;
            }
        }

        async function testLotteryDraw() {
            if (!currentToken) {
                document.getElementById('lotteryResult').className = 'result error';
                document.getElementById('lotteryResult').textContent = '❌ 请先登录';
                return;
            }

            if (!currentPeriodId) {
                document.getElementById('lotteryResult').className = 'result error';
                document.getElementById('lotteryResult').textContent = '❌ 请先获取当前期次';
                return;
            }

            try {
                const response = await fetch('http://localhost:3000/api/lottery/draw', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${currentToken}`
                    },
                    body: JSON.stringify({ periodId: currentPeriodId })
                });
                
                const data = await response.json();
                
                if (data.success) {
                    const record = data.data.record;
                    document.getElementById('lotteryResult').className = 'result success';
                    document.getElementById('lotteryResult').textContent = 
                        `🎉 抽奖成功！\n奖品: ${record.prizeName}\n金额: ${record.prizeAmount}元\n是否中奖: ${record.isWinner ? '是' : '否'}\n剩余次数: ${data.data.remainingAttempts}\n配置来源: ${data.data.configSource === 'personal' ? '个人设置' : '全局设置'}`;
                } else {
                    throw new Error(data.error || '抽奖失败');
                }
            } catch (error) {
                document.getElementById('lotteryResult').className = 'result error';
                document.getElementById('lotteryResult').textContent = `❌ 错误: ${error.message}`;
            }
        }

        // 页面加载时自动测试当前期次
        window.onload = function() {
            testCurrentPeriod();
            // 自动填入已知的用户信息进行测试
            document.getElementById('username').value = 'aa123456';
            document.getElementById('password').value = '123456';
        };
    </script>
</body>
</html>
